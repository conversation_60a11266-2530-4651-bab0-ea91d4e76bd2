import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { supabaseAdmin } from "@/lib/supabase";

/**
 * POST /api/gdpr/restriction
 * 
 * GDPR Article 18 - Right to Restriction of Processing
 * Handle user requests to restrict processing of their personal data
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth.api.getSession({ headers: headers() });
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" }, 
        { status: 401 }
      );
    }

    const body = await request.json();
    const { 
      restriction_type, 
      data_categories, 
      reason,
      processing_purposes 
    } = body;

    // Validate required fields
    if (!restriction_type || !data_categories || !reason) {
      return NextResponse.json(
        { error: "Missing required fields: restriction_type, data_categories, reason" },
        { status: 400 }
      );
    }

    const userId = session.user.id;
    const clientIP = request.headers.get('x-forwarded-for') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Create restriction request record
    const restrictionRequest = {
      user_id: userId,
      request_type: 'restriction',
      status: 'pending',
      details: {
        restriction_type, // 'temporary', 'permanent', 'conditional'
        data_categories: Array.isArray(data_categories) ? data_categories : [data_categories],
        processing_purposes: Array.isArray(processing_purposes) ? processing_purposes : [processing_purposes],
        reason,
        grounds: getRestrictionGrounds(reason)
      },
      ip_address: clientIP,
      user_agent: userAgent
    };

    // Insert restriction request into audit_logs
    const { data, error } = await supabaseAdmin
      .from('audit_logs')
      .insert({
        user_id: userId,
        action: 'restriction_request',
        resource_type: 'data_processing',
        details: restrictionRequest.details,
        ip_address: clientIP,
        user_agent: userAgent
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    // For certain restriction types, we can implement immediate restrictions
    if (restriction_type === 'marketing_communications') {
      try {
        // Update user consent to restrict marketing
        await supabaseAdmin
          .from('user_consents')
          .update({ withdrawal_date: new Date().toISOString() })
          .eq('user_id', userId)
          .eq('consent_type', 'marketing_emails')
          .is('withdrawal_date', null);

        // Log the immediate restriction
        await supabaseAdmin
          .from('audit_logs')
          .insert({
            user_id: userId,
            action: 'restriction_applied',
            resource_type: 'marketing_processing',
            details: {
              restriction_type: 'marketing_communications',
              applied_immediately: true,
              affected_processing: ['marketing_emails', 'promotional_communications']
            },
            ip_address: clientIP,
            user_agent: userAgent
          });

        return NextResponse.json({
          success: true,
          message: "Processing restriction applied successfully",
          request_id: data.id,
          status: 'completed',
          immediate_action: true,
          restricted_processing: ['marketing_communications']
        });
      } catch (restrictionError) {
        console.error('Immediate restriction failed:', restrictionError);
        // Continue with manual review process
      }
    }

    console.log(`Restriction request created for user: ${userId}, type: ${restriction_type}`);

    return NextResponse.json({
      success: true,
      message: "Processing restriction request submitted successfully. We will review and implement your request within 30 days.",
      request_id: data.id,
      status: 'pending',
      estimated_completion: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      next_steps: [
        "We will review your request and legal grounds",
        "If approved, processing restrictions will be implemented",
        "You will be notified of any decisions regarding your data"
      ]
    });

  } catch (error: any) {
    console.error('Restriction request error:', error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * GET /api/gdpr/restriction
 * 
 * Get user's restriction request history and current restrictions
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth.api.getSession({ headers: headers() });
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" }, 
        { status: 401 }
      );
    }

    const userId = session.user.id;

    // Get all restriction requests for the user
    const { data: requests, error } = await supabaseAdmin
      .from('audit_logs')
      .select('*')
      .eq('user_id', userId)
      .in('action', ['restriction_request', 'restriction_applied', 'restriction_lifted'])
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    // Get current active restrictions
    const activeRestrictions = (requests || [])
      .filter(req => req.action === 'restriction_applied')
      .map(req => {
        const details = req.details as { restriction_type?: string; affected_processing?: string[] } | null;
        return {
          id: req.id,
          restriction_type: details?.restriction_type,
          applied_date: req.created_at,
          affected_processing: details?.affected_processing || []
        };
      });

    return NextResponse.json({
      success: true,
      requests: requests || [],
      active_restrictions: activeRestrictions,
      restriction_status: activeRestrictions.length > 0 ? 'active' : 'none'
    });

  } catch (error: any) {
    console.error('Get restriction requests error:', error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/gdpr/restriction
 * 
 * Lift/remove existing processing restrictions
 */
export async function DELETE(request: NextRequest) {
  try {
    const session = await auth.api.getSession({ headers: headers() });
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" }, 
        { status: 401 }
      );
    }

    const body = await request.json();
    const { restriction_id, reason } = body;

    const userId = session.user.id;
    const clientIP = request.headers.get('x-forwarded-for') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Log the restriction lifting request
    await supabaseAdmin
      .from('audit_logs')
      .insert({
        user_id: userId,
        action: 'restriction_lifted',
        resource_type: 'data_processing',
        resource_id: restriction_id,
        details: {
          reason: reason || 'User requested to lift processing restrictions',
          lifted_by: 'user_request'
        },
        ip_address: clientIP,
        user_agent: userAgent
      });

    return NextResponse.json({
      success: true,
      message: "Processing restrictions have been lifted successfully"
    });

  } catch (error: any) {
    console.error('Lift restriction error:', error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}

// Helper function to determine legal grounds for restriction
function getRestrictionGrounds(reason: string): string[] {
  const grounds = [];
  
  if (reason.toLowerCase().includes('inaccurate') || reason.toLowerCase().includes('incorrect')) {
    grounds.push('data_accuracy_contested');
  }
  
  if (reason.toLowerCase().includes('unlawful') || reason.toLowerCase().includes('illegal')) {
    grounds.push('unlawful_processing');
  }
  
  if (reason.toLowerCase().includes('legal') || reason.toLowerCase().includes('court')) {
    grounds.push('legal_claims');
  }
  
  if (reason.toLowerCase().includes('object') || reason.toLowerCase().includes('legitimate')) {
    grounds.push('objection_pending');
  }
  
  return grounds.length > 0 ? grounds : ['user_request'];
}
