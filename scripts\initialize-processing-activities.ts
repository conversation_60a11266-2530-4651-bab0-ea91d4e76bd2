#!/usr/bin/env tsx

/**
 * Initialize Processing Activity Register
 * 
 * This script initializes the processing activity register with standard
 * activities required for GDPR Article 30 compliance.
 * 
 * Usage:
 *   bun run scripts/initialize-processing-activities.ts
 */

import { initializeStandardActivities } from '../lib/processing-activity-register';
import { supabaseAdmin } from '../lib/supabase';

/**
 * Log with timestamp
 */
function log(message: string, level: 'info' | 'warn' | 'error' = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = level === 'error' ? '❌' : level === 'warn' ? '⚠️' : 'ℹ️';
  console.log(`${prefix} [${timestamp}] ${message}`);
}

/**
 * Check if processing activities table exists
 */
async function checkTableExists(): Promise<boolean> {
  try {
    const { error } = await (supabaseAdmin as any)
      .from('processing_activities')
      .select('id')
      .limit(1);
    
    return !error;
  } catch (error) {
    return false;
  }
}

/**
 * Get current processing activities count
 */
async function getActivitiesCount(): Promise<number> {
  try {
    const { count, error } = await (supabaseAdmin as any)
      .from('processing_activities')
      .select('id', { count: 'exact', head: true });
    
    if (error) {
      throw error;
    }
    
    return count || 0;
  } catch (error) {
    log(`Failed to get activities count: ${error}`, 'error');
    return 0;
  }
}

/**
 * Main initialization function
 */
async function main() {
  log('Starting Processing Activity Register initialization');
  
  try {
    // Check if table exists
    const tableExists = await checkTableExists();
    if (!tableExists) {
      log('Processing activities table does not exist. Please run the migration first:', 'error');
      log('supabase migration up', 'error');
      process.exit(1);
    }
    
    log('Processing activities table found');
    
    // Get current count
    const currentCount = await getActivitiesCount();
    log(`Current processing activities count: ${currentCount}`);
    
    // Initialize standard activities
    log('Initializing standard processing activities...');
    await initializeStandardActivities();
    
    // Get new count
    const newCount = await getActivitiesCount();
    const addedCount = newCount - currentCount;
    
    if (addedCount > 0) {
      log(`Successfully added ${addedCount} new processing activities`);
    } else {
      log('No new processing activities were added (they may already exist)');
    }
    
    log(`Total processing activities: ${newCount}`);
    
    // List all activities
    const { data: activities, error } = await (supabaseAdmin as any)
      .from('processing_activities')
      .select('activity_name, system_generated, created_at')
      .order('created_at', { ascending: false });
    
    if (error) {
      throw error;
    }
    
    log('\nCurrent Processing Activities:');
    activities?.forEach((activity: any, index: number) => {
      const type = activity.system_generated ? '[SYSTEM]' : '[CUSTOM]';
      const date = new Date(activity.created_at).toLocaleDateString();
      log(`  ${index + 1}. ${type} ${activity.activity_name} (${date})`);
    });
    
    log('\nProcessing Activity Register initialization completed successfully! 🎉');
    
    // Log the initialization to audit logs
    await supabaseAdmin
      .from('audit_logs')
      .insert({
        user_id: null, // System action
        action: 'processing_activities_initialized',
        resource_type: 'processing_activity_register',
        details: {
          script_run: true,
          activities_added: addedCount,
          total_activities: newCount,
          initialization_date: new Date().toISOString()
        }
      });
    
  } catch (error: any) {
    log(`Initialization failed: ${error.message}`, 'error');
    
    // Log the error
    try {
      await supabaseAdmin
        .from('audit_logs')
        .insert({
          user_id: null,
          action: 'processing_activities_initialization_error',
          resource_type: 'processing_activity_register',
          details: {
            error: error.message,
            stack: error.stack,
            script_run: true
          }
        });
    } catch (logError) {
      log(`Failed to log error: ${logError}`, 'error');
    }
    
    process.exit(1);
  }
}

/**
 * Display help information
 */
function showHelp() {
  console.log(`
Processing Activity Register Initialization Script

Usage: bun run scripts/initialize-processing-activities.ts

This script initializes the processing activity register with standard
activities required for GDPR Article 30 compliance.

Standard Activities:
- User Account Management
- Payment Processing  
- Marketing Communications
- Website Analytics
- Customer Support

Prerequisites:
- Processing activities table must exist (run migration first)
- Supabase connection must be configured

Environment Variables:
- SUPABASE_URL: Your Supabase project URL
- SUPABASE_SERVICE_ROLE_KEY: Service role key for admin operations
`);
}

// Run the script if called directly
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    process.exit(0);
  }
  
  main().catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
}

export { main as initializeProcessingActivities };
