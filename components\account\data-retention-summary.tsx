"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Clock, 
  Database, 
  AlertTriangle, 
  CheckCircle,
  Loader2,
  ArrowRight,
  Calendar,
  Shield
} from 'lucide-react';
import { toast } from 'sonner';
import Link from 'next/link';

interface DataRetentionSummary {
  totalDataTypes: number;
  activeRetentions: number;
  pendingCleanups: number;
  oldestData: string | null;
  nextScheduledCleanup: string | null;
}

interface UpcomingCleanup {
  dataType: string;
  cleanupDate: string;
  recordCount: number;
  deletionMethod: string;
}

export function DataRetentionSummary() {
  const [summary, setSummary] = useState<DataRetentionSummary | null>(null);
  const [upcomingCleanups, setUpcomingCleanups] = useState<UpcomingCleanup[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchRetentionSummary();
  }, []);

  const fetchRetentionSummary = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/account/data-retention');
      
      if (!response.ok) {
        if (response.status === 401) {
          return; // User not authenticated, skip silently
        }
        throw new Error('Failed to fetch data retention summary');
      }

      const data = await response.json();
      setSummary(data.summary || null);
      
      // Extract upcoming cleanups from retention data
      const cleanups = (data.retentionData || [])
        .filter((item: any) => item.nextCleanupDate && item.status === 'pending_cleanup')
        .map((item: any) => ({
          dataType: item.dataType,
          cleanupDate: item.nextCleanupDate,
          recordCount: item.recordCount,
          deletionMethod: item.deletionMethod
        }))
        .sort((a: any, b: any) => new Date(a.cleanupDate).getTime() - new Date(b.cleanupDate).getTime())
        .slice(0, 3); // Show only next 3 cleanups
      
      setUpcomingCleanups(cleanups);
    } catch (error: any) {
      console.error('Failed to fetch retention summary:', error);
      // Don't show error toast for summary component
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not scheduled';
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const getDeletionMethodBadge = (method: string) => {
    const colors: Record<string, string> = {
      'hard_delete': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
      'anonymize': 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
      'archive': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
    };

    return (
      <Badge className={colors[method] || 'bg-gray-100 text-gray-800'}>
        {method.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
      </Badge>
    );
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <Loader2 className="w-5 h-5 animate-spin" />
            <span className="ml-2 text-sm">Loading retention info...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!summary) {
    return null; // Don't show anything if no data
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Clock className="w-5 h-5 text-blue-500" />
            Data Retention
          </div>
          <Link href="/account/data-retention">
            <Button variant="ghost" size="sm">
              <ArrowRight className="w-4 h-4" />
            </Button>
          </Link>
        </CardTitle>
        <CardDescription>
          How we manage your personal data lifecycle
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Summary Stats */}
        <div className="grid gap-3 md:grid-cols-3">
          <div className="text-center p-3 border rounded-lg">
            <div className="text-lg font-semibold text-blue-600">{summary.totalDataTypes}</div>
            <p className="text-xs text-muted-foreground">Data Types</p>
          </div>
          <div className="text-center p-3 border rounded-lg">
            <div className="text-lg font-semibold text-green-600">{summary.activeRetentions}</div>
            <p className="text-xs text-muted-foreground">Active</p>
          </div>
          <div className="text-center p-3 border rounded-lg">
            <div className="text-lg font-semibold text-yellow-600">{summary.pendingCleanups}</div>
            <p className="text-xs text-muted-foreground">Pending</p>
          </div>
        </div>

        {/* Next Cleanup */}
        {summary.nextScheduledCleanup && (
          <div className="p-3 border rounded-lg bg-yellow-50 dark:bg-yellow-950">
            <div className="flex items-center gap-2 mb-1">
              <Calendar className="w-4 h-4 text-yellow-600" />
              <span className="font-medium text-sm">Next Cleanup</span>
            </div>
            <p className="text-sm text-muted-foreground">
              {formatDate(summary.nextScheduledCleanup)}
            </p>
          </div>
        )}

        {/* Upcoming Cleanups */}
        {upcomingCleanups.length > 0 && (
          <div>
            <h4 className="font-medium text-sm mb-2">Upcoming Cleanups</h4>
            <div className="space-y-2">
              {upcomingCleanups.map((cleanup, index) => (
                <div key={index} className="flex items-center justify-between p-2 border rounded text-sm">
                  <div className="flex-1">
                    <p className="font-medium">{cleanup.dataType}</p>
                    <p className="text-xs text-muted-foreground">
                      {cleanup.recordCount} records • {formatDate(cleanup.cleanupDate)}
                    </p>
                  </div>
                  {getDeletionMethodBadge(cleanup.deletionMethod)}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Status Indicator */}
        <div className="flex items-center justify-between pt-2 border-t">
          <div className="flex items-center gap-2">
            {summary.pendingCleanups > 0 ? (
              <>
                <AlertTriangle className="w-4 h-4 text-yellow-500" />
                <span className="text-sm text-yellow-700 dark:text-yellow-300">
                  {summary.pendingCleanups} cleanup{summary.pendingCleanups > 1 ? 's' : ''} pending
                </span>
              </>
            ) : (
              <>
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span className="text-sm text-green-700 dark:text-green-300">
                  All data retention up to date
                </span>
              </>
            )}
          </div>
          <Link href="/account/data-retention">
            <Button variant="outline" size="sm">
              View Details
            </Button>
          </Link>
        </div>

        {/* Info */}
        <div className="bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
          <div className="flex items-start gap-2">
            <Shield className="w-4 h-4 text-blue-500 mt-0.5" />
            <div className="text-sm">
              <p className="font-medium text-blue-900 dark:text-blue-100 mb-1">
                GDPR Compliant
              </p>
              <p className="text-blue-800 dark:text-blue-200">
                We automatically manage your data lifecycle according to legal requirements 
                and your privacy preferences.
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
