"use client";

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { 
  Shield, 
  Cookie, 
  Mail, 
  BarChart3, 
  Target, 
  Clock, 
  CheckCircle, 
  XCircle,
  Loader2,
  AlertTriangle,
  Info,
  History
} from 'lucide-react';
import { toast } from 'sonner';

interface ConsentData {
  privacy_policy?: {
    given: boolean;
    date: string;
    id: string;
  };
  terms_of_service?: {
    given: boolean;
    date: string;
    id: string;
  };
  marketing_emails?: {
    given: boolean;
    date: string;
    id: string;
  };
}

interface CookieConsentData {
  necessary_cookies: boolean;
  analytics_cookies: boolean;
  marketing_cookies: boolean;
  consent_date: string | null;
}

interface ConsentStatus {
  gdprConsents: ConsentData;
  cookieConsents: CookieConsentData;
  consentHistory: any[];
}

export function ConsentManagementDashboard() {
  const [consentStatus, setConsentStatus] = useState<ConsentStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const [showHistory, setShowHistory] = useState(false);

  // Local state for consent toggles
  const [localGdprConsents, setLocalGdprConsents] = useState<{
    marketing_emails: boolean;
  }>({
    marketing_emails: false
  });

  const [localCookieConsents, setLocalCookieConsents] = useState<{
    analytics_cookies: boolean;
    marketing_cookies: boolean;
  }>({
    analytics_cookies: false,
    marketing_cookies: false
  });

  useEffect(() => {
    fetchConsentStatus();
  }, []);

  const fetchConsentStatus = async () => {
    try {
      setIsLoading(true);
      
      // Fetch both GDPR consents and cookie consents
      const [gdprResponse, cookieResponse] = await Promise.all([
        fetch('/api/gdpr/consent'),
        fetch('/api/gdpr/cookie-consent')
      ]);

      if (!gdprResponse.ok || !cookieResponse.ok) {
        if (gdprResponse.status === 401 || cookieResponse.status === 401) {
          toast.error('Please sign in to manage your consent preferences');
          return;
        }
        throw new Error('Failed to fetch consent status');
      }

      const [gdprData, cookieData] = await Promise.all([
        gdprResponse.json(),
        cookieResponse.json()
      ]);

      const status: ConsentStatus = {
        gdprConsents: gdprData.consents || {},
        cookieConsents: cookieData.consent || {
          necessary_cookies: true,
          analytics_cookies: false,
          marketing_cookies: false,
          consent_date: null
        },
        consentHistory: gdprData.consent_history || []
      };

      setConsentStatus(status);

      // Update local state
      setLocalGdprConsents({
        marketing_emails: status.gdprConsents.marketing_emails?.given || false
      });

      setLocalCookieConsents({
        analytics_cookies: status.cookieConsents.analytics_cookies,
        marketing_cookies: status.cookieConsents.marketing_cookies
      });

    } catch (error: any) {
      toast.error(error.message || 'Failed to fetch consent status');
    } finally {
      setIsLoading(false);
    }
  };

  const updateGdprConsents = async () => {
    setIsUpdating(true);
    try {
      const response = await fetch('/api/gdpr/consent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          consents: {
            privacy_policy: true, // Always keep required consents
            terms_of_service: true, // Always keep required consents
            marketing_emails: localGdprConsents.marketing_emails,
          }
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update GDPR consents');
      }

      toast.success('GDPR consent preferences updated successfully');
      await fetchConsentStatus(); // Refresh data
    } catch (error: any) {
      toast.error(error.message || 'Failed to update GDPR consents');
    } finally {
      setIsUpdating(false);
    }
  };

  const updateCookieConsents = async () => {
    setIsUpdating(true);
    try {
      const response = await fetch('/api/gdpr/cookie-consent', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          necessary_cookies: true, // Always required
          analytics_cookies: localCookieConsents.analytics_cookies,
          marketing_cookies: localCookieConsents.marketing_cookies,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update cookie consents');
      }

      toast.success('Cookie preferences updated successfully');
      await fetchConsentStatus(); // Refresh data
    } catch (error: any) {
      toast.error(error.message || 'Failed to update cookie preferences');
    } finally {
      setIsUpdating(false);
    }
  };

  const withdrawAllConsents = async () => {
    if (!confirm('Are you sure you want to withdraw all optional consents? This will disable marketing communications and optional cookies.')) {
      return;
    }

    setIsUpdating(true);
    try {
      // Withdraw GDPR consents (except required ones)
      await fetch('/api/gdpr/consent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          consents: {
            privacy_policy: true, // Keep required
            terms_of_service: true, // Keep required
            marketing_emails: false, // Withdraw optional
          }
        }),
      });

      // Reset cookies to necessary only
      await fetch('/api/gdpr/cookie-consent', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          necessary_cookies: true,
          analytics_cookies: false,
          marketing_cookies: false,
        }),
      });

      toast.success('All optional consents have been withdrawn');
      await fetchConsentStatus(); // Refresh data
    } catch (error: any) {
      toast.error(error.message || 'Failed to withdraw consents');
    } finally {
      setIsUpdating(false);
    }
  };

  const getConsentStatusBadge = (given: boolean, required: boolean = false) => {
    if (required) {
      return <Badge variant="secondary">Required</Badge>;
    }
    return given ? (
      <Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
        <CheckCircle className="w-3 h-3 mr-1" />
        Granted
      </Badge>
    ) : (
      <Badge variant="outline" className="text-gray-600">
        <XCircle className="w-3 h-3 mr-1" />
        Withdrawn
      </Badge>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="w-8 h-8 animate-spin" />
        <span className="ml-2">Loading consent preferences...</span>
      </div>
    );
  }

  if (!consentStatus) {
    return (
      <div className="text-center p-8">
        <AlertTriangle className="w-12 h-12 mx-auto mb-4 text-yellow-500" />
        <p>Failed to load consent preferences</p>
        <Button onClick={fetchConsentStatus} className="mt-4">
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold">Consent Management</h2>
          <p className="text-muted-foreground">
            Manage your data processing and cookie preferences
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setShowHistory(!showHistory)}
          >
            <History className="w-4 h-4 mr-2" />
            {showHistory ? 'Hide' : 'Show'} History
          </Button>
          <Button
            variant="destructive"
            onClick={withdrawAllConsents}
            disabled={isUpdating}
          >
            {isUpdating ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <XCircle className="w-4 h-4 mr-2" />
            )}
            Withdraw All Optional
          </Button>
        </div>
      </div>

      {/* GDPR Consents */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5 text-blue-500" />
            GDPR Data Processing Consents
          </CardTitle>
          <CardDescription>
            Your consent for different types of data processing activities
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Privacy Policy - Required */}
          <div className="flex items-center justify-between p-4 border rounded-lg bg-gray-50 dark:bg-gray-900">
            <div className="flex-1">
              <h4 className="font-medium">Privacy Policy Agreement</h4>
              <p className="text-sm text-muted-foreground">
                Required for using our service and processing your account data
              </p>
              <div className="flex items-center gap-2 mt-2">
                {getConsentStatusBadge(true, true)}
                {consentStatus.gdprConsents.privacy_policy && (
                  <span className="text-xs text-muted-foreground">
                    Agreed on {new Date(consentStatus.gdprConsents.privacy_policy.date).toLocaleDateString()}
                  </span>
                )}
              </div>
            </div>
            <Switch checked={true} disabled />
          </div>

          {/* Terms of Service - Required */}
          <div className="flex items-center justify-between p-4 border rounded-lg bg-gray-50 dark:bg-gray-900">
            <div className="flex-1">
              <h4 className="font-medium">Terms of Service Agreement</h4>
              <p className="text-sm text-muted-foreground">
                Required for using our service and defining our relationship
              </p>
              <div className="flex items-center gap-2 mt-2">
                {getConsentStatusBadge(true, true)}
                {consentStatus.gdprConsents.terms_of_service && (
                  <span className="text-xs text-muted-foreground">
                    Agreed on {new Date(consentStatus.gdprConsents.terms_of_service.date).toLocaleDateString()}
                  </span>
                )}
              </div>
            </div>
            <Switch checked={true} disabled />
          </div>

          {/* Marketing Emails - Optional */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex-1">
              <h4 className="font-medium flex items-center gap-2">
                <Mail className="w-4 h-4" />
                Marketing Communications
              </h4>
              <p className="text-sm text-muted-foreground">
                Receive promotional emails, product updates, and special offers
              </p>
              <div className="flex items-center gap-2 mt-2">
                {getConsentStatusBadge(localGdprConsents.marketing_emails)}
                {consentStatus.gdprConsents.marketing_emails && (
                  <span className="text-xs text-muted-foreground">
                    Last updated {new Date(consentStatus.gdprConsents.marketing_emails.date).toLocaleDateString()}
                  </span>
                )}
              </div>
            </div>
            <Switch
              checked={localGdprConsents.marketing_emails}
              onCheckedChange={(checked) => 
                setLocalGdprConsents(prev => ({ ...prev, marketing_emails: checked }))
              }
              disabled={isUpdating}
            />
          </div>

          <Button
            onClick={updateGdprConsents}
            disabled={isUpdating}
            className="w-full"
          >
            {isUpdating ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Shield className="w-4 h-4 mr-2" />
            )}
            Update GDPR Preferences
          </Button>
        </CardContent>
      </Card>

      {/* Cookie Consents */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Cookie className="w-5 h-5 text-orange-500" />
            Cookie Preferences
          </CardTitle>
          <CardDescription>
            Control which cookies we can use to enhance your experience
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Necessary Cookies - Required */}
          <div className="flex items-center justify-between p-4 border rounded-lg bg-gray-50 dark:bg-gray-900">
            <div className="flex-1">
              <h4 className="font-medium">Necessary Cookies</h4>
              <p className="text-sm text-muted-foreground">
                Essential for website functionality, security, and your account access
              </p>
              <div className="flex items-center gap-2 mt-2">
                {getConsentStatusBadge(true, true)}
                <span className="text-xs text-muted-foreground">
                  Always active for security and functionality
                </span>
              </div>
            </div>
            <Switch checked={true} disabled />
          </div>

          {/* Analytics Cookies - Optional */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex-1">
              <h4 className="font-medium flex items-center gap-2">
                <BarChart3 className="w-4 h-4" />
                Analytics Cookies
              </h4>
              <p className="text-sm text-muted-foreground">
                Help us understand how you use our website to improve your experience
              </p>
              <div className="flex items-center gap-2 mt-2">
                {getConsentStatusBadge(localCookieConsents.analytics_cookies)}
                {consentStatus.cookieConsents.consent_date && (
                  <span className="text-xs text-muted-foreground">
                    Last updated {new Date(consentStatus.cookieConsents.consent_date).toLocaleDateString()}
                  </span>
                )}
              </div>
            </div>
            <Switch
              checked={localCookieConsents.analytics_cookies}
              onCheckedChange={(checked) =>
                setLocalCookieConsents(prev => ({ ...prev, analytics_cookies: checked }))
              }
              disabled={isUpdating}
            />
          </div>

          {/* Marketing Cookies - Optional */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex-1">
              <h4 className="font-medium flex items-center gap-2">
                <Target className="w-4 h-4" />
                Marketing Cookies
              </h4>
              <p className="text-sm text-muted-foreground">
                Used to show you relevant advertisements and measure campaign effectiveness
              </p>
              <div className="flex items-center gap-2 mt-2">
                {getConsentStatusBadge(localCookieConsents.marketing_cookies)}
                {consentStatus.cookieConsents.consent_date && (
                  <span className="text-xs text-muted-foreground">
                    Last updated {new Date(consentStatus.cookieConsents.consent_date).toLocaleDateString()}
                  </span>
                )}
              </div>
            </div>
            <Switch
              checked={localCookieConsents.marketing_cookies}
              onCheckedChange={(checked) =>
                setLocalCookieConsents(prev => ({ ...prev, marketing_cookies: checked }))
              }
              disabled={isUpdating}
            />
          </div>

          <Button
            onClick={updateCookieConsents}
            disabled={isUpdating}
            className="w-full"
          >
            {isUpdating ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Cookie className="w-4 h-4 mr-2" />
            )}
            Update Cookie Preferences
          </Button>
        </CardContent>
      </Card>

      {/* Consent History */}
      {showHistory && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <History className="w-5 h-5 text-purple-500" />
              Consent History
            </CardTitle>
            <CardDescription>
              Complete history of your consent decisions for audit and transparency
            </CardDescription>
          </CardHeader>
          <CardContent>
            {consentStatus.consentHistory.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Info className="w-8 h-8 mx-auto mb-2" />
                <p>No consent history available</p>
              </div>
            ) : (
              <div className="space-y-3">
                {consentStatus.consentHistory.slice(0, 10).map((consent: any, index: number) => (
                  <div key={consent.id || index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium capitalize">
                          {consent.consent_type?.replace('_', ' ')}
                        </span>
                        {consent.consent_given ? (
                          <Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            Granted
                          </Badge>
                        ) : (
                          <Badge variant="outline">
                            Withdrawn
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-center gap-4 mt-1 text-sm text-muted-foreground">
                        <span className="flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          {new Date(consent.consent_date).toLocaleString()}
                        </span>
                        {consent.withdrawal_date && (
                          <span className="flex items-center gap-1">
                            <XCircle className="w-3 h-3" />
                            Withdrawn {new Date(consent.withdrawal_date).toLocaleString()}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
                {consentStatus.consentHistory.length > 10 && (
                  <p className="text-center text-sm text-muted-foreground">
                    Showing 10 most recent entries of {consentStatus.consentHistory.length} total
                  </p>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Information Card */}
      <Card className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <Info className="w-5 h-5 text-blue-500 mt-0.5" />
            <div className="text-sm">
              <p className="font-medium text-blue-900 dark:text-blue-100 mb-1">
                Your Privacy Rights
              </p>
              <p className="text-blue-800 dark:text-blue-200">
                You can change these preferences at any time. Withdrawing consent will not affect
                the lawfulness of processing based on consent before its withdrawal. For more information,
                see our <a href="/privacy" className="underline">Privacy Policy</a>.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
