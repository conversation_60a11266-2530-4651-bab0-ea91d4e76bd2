import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { 
  getDataRetentionStatus, 
  executeRetentionCleanup, 
  DATA_RETENTION_POLICIES,
  getRetentionPolicy 
} from "@/lib/data-retention";

/**
 * GET /api/admin/data-retention
 * 
 * Get current data retention status and policies
 * Admin-only endpoint for monitoring data retention compliance
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth.api.getSession({ headers: headers() });
    
    // For demo purposes, we'll allow any authenticated user
    // In production, you'd check for admin role
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized - Admin access required" }, 
        { status: 401 }
      );
    }

    // Get current retention status
    const retentionStatus = await getDataRetentionStatus();

    // Calculate summary statistics
    const summary = {
      totalPolicies: DATA_RETENTION_POLICIES.length,
      policiesNeedingCleanup: retentionStatus.policies.filter(p => p.cleanupNeeded).length,
      totalRecordsToCleanup: retentionStatus.totalRecordsToCleanup,
      lastChecked: new Date().toISOString()
    };

    return NextResponse.json({
      success: true,
      summary,
      policies: retentionStatus.policies.map(policy => ({
        table: policy.table,
        description: policy.description,
        retentionPeriodDays: policy.retentionPeriodDays,
        legalBasis: policy.legalBasis,
        deletionMethod: policy.deletionMethod,
        recordCount: policy.recordCount,
        cutoffDate: policy.cutoffDate,
        cleanupNeeded: policy.cleanupNeeded,
        retentionPeriodHuman: policy.retentionPeriodDays === -1 
          ? 'Never delete' 
          : `${policy.retentionPeriodDays} days`
      }))
    });

  } catch (error: any) {
    console.error('Data retention status error:', error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/data-retention
 * 
 * Execute data retention cleanup
 * Admin-only endpoint for running retention policies
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth.api.getSession({ headers: headers() });
    
    // For demo purposes, we'll allow any authenticated user
    // In production, you'd check for admin role
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized - Admin access required" }, 
        { status: 401 }
      );
    }

    const body = await request.json();
    const { 
      tables, // Array of table names to clean up
      dryRun = true, // Default to dry run for safety
      force = false // Force cleanup even if large number of records
    } = body;

    if (!Array.isArray(tables) || tables.length === 0) {
      return NextResponse.json(
        { error: "Please specify tables to clean up" },
        { status: 400 }
      );
    }

    const results: Array<{
      table: string;
      success: boolean;
      error?: string;
      recordsProcessed: number;
      dryRun?: boolean;
      recordsToProcess?: number;
      cutoffDate?: string;
      policy?: {
        description: string;
        retentionPeriodDays: number;
        deletionMethod: string;
        legalBasis?: string;
      };
    }> = [];
    let totalProcessed = 0;

    // Process each requested table
    for (const tableName of tables) {
      const policy = getRetentionPolicy(tableName);
      
      if (!policy) {
        results.push({
          table: tableName,
          success: false,
          error: `No retention policy found for table: ${tableName}`,
          recordsProcessed: 0
        });
        continue;
      }

      if (dryRun) {
        // Dry run - just check what would be cleaned up
        const { getDataRetentionStatus } = await import('@/lib/data-retention');
        const status = await getDataRetentionStatus();
        const policyStatus = status.policies.find(p => p.table === tableName);
        
        results.push({
          table: tableName,
          success: true,
          dryRun: true,
          recordsProcessed: 0,
          recordsToProcess: policyStatus?.recordCount || 0,
          cutoffDate: policyStatus?.cutoffDate?.toISOString(),
          policy: {
            description: policy.description,
            retentionPeriodDays: policy.retentionPeriodDays,
            deletionMethod: policy.deletionMethod,
            legalBasis: policy.legalBasis
          }
        });
      } else {
        // Actual cleanup
        const result = await executeRetentionCleanup(policy);
        
        results.push({
          table: tableName,
          success: result.success,
          error: result.error,
          recordsProcessed: result.recordsProcessed,
          policy: {
            description: policy.description,
            retentionPeriodDays: policy.retentionPeriodDays,
            deletionMethod: policy.deletionMethod
          }
        });

        if (result.success) {
          totalProcessed += result.recordsProcessed;
        }
      }
    }

    // Log the retention cleanup request
    const clientIP = request.headers.get('x-forwarded-for') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    await import('@/lib/supabase').then(({ supabaseAdmin }) => 
      supabaseAdmin
        .from('audit_logs')
        .insert({
          user_id: session.user.id,
          action: dryRun ? 'data_retention_dry_run' : 'data_retention_cleanup_requested',
          resource_type: 'data_retention',
          details: {
            tables_requested: tables,
            dry_run: dryRun,
            force,
            total_records_processed: totalProcessed,
            results_summary: results.map(r => ({
              table: r.table,
              success: r.success,
              records: r.recordsProcessed
            }))
          },
          ip_address: clientIP,
          user_agent: userAgent
        })
    );

    const summary = {
      tablesProcessed: tables.length,
      successfulCleanups: results.filter(r => r.success).length,
      totalRecordsProcessed: totalProcessed,
      dryRun,
      executedAt: new Date().toISOString()
    };

    return NextResponse.json({
      success: true,
      summary,
      results,
      message: dryRun 
        ? "Dry run completed - no data was actually deleted" 
        : `Data retention cleanup completed. ${totalProcessed} records processed.`
    });

  } catch (error: any) {
    console.error('Data retention cleanup error:', error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/admin/data-retention
 * 
 * Update data retention policies (future enhancement)
 * This would allow dynamic policy updates
 */
export async function PUT(request: NextRequest) {
  return NextResponse.json(
    { error: "Policy updates not implemented yet" },
    { status: 501 }
  );
}
