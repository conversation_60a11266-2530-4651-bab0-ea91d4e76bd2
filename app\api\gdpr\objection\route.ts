import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { supabaseAdmin } from "@/lib/supabase";

/**
 * POST /api/gdpr/objection
 * 
 * GDPR Article 21 - Right to Object
 * Handle user objections to processing of their personal data
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth.api.getSession({ headers: headers() });
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" }, 
        { status: 401 }
      );
    }

    const body = await request.json();
    const { 
      objection_type, 
      processing_purposes, 
      reason,
      legal_basis,
      data_categories 
    } = body;

    // Validate required fields
    if (!objection_type || !processing_purposes || !reason) {
      return NextResponse.json(
        { error: "Missing required fields: objection_type, processing_purposes, reason" },
        { status: 400 }
      );
    }

    const userId = session.user.id;
    const clientIP = request.headers.get('x-forwarded-for') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Create objection request record
    const objectionRequest = {
      user_id: userId,
      request_type: 'objection',
      status: 'pending',
      details: {
        objection_type, // 'direct_marketing', 'legitimate_interests', 'public_task', 'scientific_research'
        processing_purposes: Array.isArray(processing_purposes) ? processing_purposes : [processing_purposes],
        data_categories: Array.isArray(data_categories) ? data_categories : [data_categories],
        reason,
        legal_basis: legal_basis || 'article_21_gdpr',
        grounds: getObjectionGrounds(objection_type, reason)
      },
      ip_address: clientIP,
      user_agent: userAgent
    };

    // Insert objection request into audit_logs
    const { data, error } = await supabaseAdmin
      .from('audit_logs')
      .insert({
        user_id: userId,
        action: 'objection_request',
        resource_type: 'data_processing',
        details: objectionRequest.details,
        ip_address: clientIP,
        user_agent: userAgent
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    // Handle direct marketing objections immediately (GDPR requires immediate cessation)
    if (objection_type === 'direct_marketing') {
      try {
        // Withdraw marketing consent immediately
        await supabaseAdmin
          .from('user_consents')
          .update({ withdrawal_date: new Date().toISOString() })
          .eq('user_id', userId)
          .eq('consent_type', 'marketing_emails')
          .is('withdrawal_date', null);

        // Update cookie consent to disable marketing cookies
        await supabaseAdmin
          .from('cookie_consents')
          .insert({
            user_id: userId,
            necessary_cookies: true,
            analytics_cookies: true, // Keep analytics unless specifically objected to
            marketing_cookies: false, // Disable marketing cookies
            consent_date: new Date().toISOString(),
            ip_address: clientIP,
            user_agent: userAgent
          });

        // Log the immediate objection implementation
        await supabaseAdmin
          .from('audit_logs')
          .insert({
            user_id: userId,
            action: 'objection_implemented',
            resource_type: 'marketing_processing',
            details: {
              objection_type: 'direct_marketing',
              implemented_immediately: true,
              stopped_processing: ['marketing_emails', 'promotional_communications', 'marketing_cookies'],
              legal_requirement: 'GDPR Article 21(3) - immediate cessation required'
            },
            ip_address: clientIP,
            user_agent: userAgent
          });

        return NextResponse.json({
          success: true,
          message: "Direct marketing objection processed successfully. All marketing communications have been stopped immediately.",
          request_id: data.id,
          status: 'completed',
          immediate_action: true,
          stopped_processing: ['marketing_emails', 'promotional_communications', 'marketing_cookies']
        });
      } catch (objectionError) {
        console.error('Immediate objection implementation failed:', objectionError);
        // Continue with manual review process
      }
    }

    // For other objection types, create a review process
    const responseMessage = getObjectionResponseMessage(objection_type);
    const estimatedCompletion = objection_type === 'direct_marketing' 
      ? new Date(Date.now() + 24 * 60 * 60 * 1000) // 1 day for marketing
      : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days for others

    console.log(`Objection request created for user: ${userId}, type: ${objection_type}`);

    return NextResponse.json({
      success: true,
      message: responseMessage,
      request_id: data.id,
      status: 'pending',
      estimated_completion: estimatedCompletion.toISOString(),
      next_steps: getNextSteps(objection_type)
    });

  } catch (error: any) {
    console.error('Objection request error:', error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * GET /api/gdpr/objection
 * 
 * Get user's objection request history and current objections
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth.api.getSession({ headers: headers() });
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" }, 
        { status: 401 }
      );
    }

    const userId = session.user.id;

    // Get all objection requests for the user
    const { data: requests, error } = await supabaseAdmin
      .from('audit_logs')
      .select('*')
      .eq('user_id', userId)
      .in('action', ['objection_request', 'objection_implemented', 'objection_overridden'])
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    // Get current active objections
    const activeObjections = (requests || [])
      .filter(req => req.action === 'objection_implemented')
      .map(req => {
        const details = req.details as { objection_type?: string; stopped_processing?: string[] } | null;
        return {
          id: req.id,
          objection_type: details?.objection_type,
          implemented_date: req.created_at,
          stopped_processing: details?.stopped_processing || []
        };
      });

    return NextResponse.json({
      success: true,
      requests: requests || [],
      active_objections: activeObjections,
      objection_status: activeObjections.length > 0 ? 'active' : 'none'
    });

  } catch (error: any) {
    console.error('Get objection requests error:', error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}

// Helper functions
function getObjectionGrounds(objectionType: string, reason: string): string[] {
  const grounds = [];
  
  switch (objectionType) {
    case 'direct_marketing':
      grounds.push('article_21_2_direct_marketing');
      break;
    case 'legitimate_interests':
      grounds.push('article_21_1_legitimate_interests');
      if (reason.toLowerCase().includes('particular situation')) {
        grounds.push('particular_situation');
      }
      break;
    case 'public_task':
      grounds.push('article_21_1_public_task');
      break;
    case 'scientific_research':
      grounds.push('article_21_6_scientific_research');
      break;
    default:
      grounds.push('general_objection');
  }
  
  return grounds;
}

function getObjectionResponseMessage(objectionType: string): string {
  switch (objectionType) {
    case 'direct_marketing':
      return "Direct marketing objection submitted. Marketing communications will be stopped immediately as required by GDPR.";
    case 'legitimate_interests':
      return "Objection to legitimate interests processing submitted. We will assess whether we have compelling legitimate grounds that override your interests.";
    case 'public_task':
      return "Objection to public task processing submitted. We will review your particular situation and grounds for objection.";
    case 'scientific_research':
      return "Objection to scientific research processing submitted. We will assess whether the research is necessary for public interest reasons.";
    default:
      return "Your objection has been submitted successfully. We will review and respond within the required timeframe.";
  }
}

function getNextSteps(objectionType: string): string[] {
  const commonSteps = [
    "We will review your objection and legal grounds",
    "You will be notified of our decision and any actions taken"
  ];

  switch (objectionType) {
    case 'direct_marketing':
      return [
        "Marketing communications stopped immediately",
        ...commonSteps
      ];
    case 'legitimate_interests':
      return [
        "We will assess our legitimate interests against your rights",
        "Processing may continue if we demonstrate compelling legitimate grounds",
        ...commonSteps
      ];
    default:
      return commonSteps;
  }
}
