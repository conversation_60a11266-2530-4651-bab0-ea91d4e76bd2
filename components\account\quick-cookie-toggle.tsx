"use client";

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { 
  <PERSON>ie, 
  BarChart3, 
  Target, 
  CheckCircle, 
  XCircle,
  Loader2
} from 'lucide-react';
import { toast } from 'sonner';

interface QuickCookieToggleProps {
  showLabels?: boolean;
  compact?: boolean;
  onUpdate?: () => void;
}

interface CookieState {
  analytics_cookies: boolean;
  marketing_cookies: boolean;
}

export function QuickCookieToggle({ 
  showLabels = true, 
  compact = false,
  onUpdate 
}: QuickCookieToggleProps) {
  const [cookieState, setCookieState] = useState<CookieState>({
    analytics_cookies: false,
    marketing_cookies: false
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);

  useEffect(() => {
    fetchCookieState();
  }, []);

  const fetchCookieState = async () => {
    try {
      const response = await fetch('/api/gdpr/cookie-consent');
      
      if (response.ok) {
        const data = await response.json();
        const consent = data.consent || {};
        setCookieState({
          analytics_cookies: consent.analytics_cookies || false,
          marketing_cookies: consent.marketing_cookies || false
        });
      }
    } catch (error) {
      console.error('Failed to fetch cookie state:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const updateCookieSetting = async (type: 'analytics_cookies' | 'marketing_cookies', value: boolean) => {
    setIsUpdating(true);
    try {
      const newState = { ...cookieState, [type]: value };
      
      const response = await fetch('/api/gdpr/cookie-consent', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          necessary_cookies: true,
          analytics_cookies: newState.analytics_cookies,
          marketing_cookies: newState.marketing_cookies,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update cookie setting');
      }

      setCookieState(newState);
      toast.success(`${type === 'analytics_cookies' ? 'Analytics' : 'Marketing'} cookies ${value ? 'enabled' : 'disabled'}`);
      onUpdate?.();
    } catch (error: any) {
      toast.error(error.message || 'Failed to update cookie setting');
    } finally {
      setIsUpdating(false);
    }
  };

  const getStatusBadge = (enabled: boolean) => {
    return enabled ? (
      <Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
        <CheckCircle className="w-3 h-3 mr-1" />
        On
      </Badge>
    ) : (
      <Badge variant="outline" className="text-gray-600">
        <XCircle className="w-3 h-3 mr-1" />
        Off
      </Badge>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center gap-2">
        <Loader2 className="w-4 h-4 animate-spin" />
        <span className="text-sm text-muted-foreground">Loading...</span>
      </div>
    );
  }

  if (compact) {
    return (
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <BarChart3 className="w-4 h-4 text-green-500" />
          <Switch
            checked={cookieState.analytics_cookies}
            onCheckedChange={(checked) => updateCookieSetting('analytics_cookies', checked)}
            disabled={isUpdating}
          />
          {showLabels && <span className="text-sm">Analytics</span>}
        </div>
        <div className="flex items-center gap-2">
          <Target className="w-4 h-4 text-purple-500" />
          <Switch
            checked={cookieState.marketing_cookies}
            onCheckedChange={(checked) => updateCookieSetting('marketing_cookies', checked)}
            disabled={isUpdating}
          />
          {showLabels && <span className="text-sm">Marketing</span>}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {/* Analytics Cookies */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <BarChart3 className="w-4 h-4 text-green-500" />
          {showLabels && <span className="text-sm font-medium">Analytics Cookies</span>}
          {getStatusBadge(cookieState.analytics_cookies)}
        </div>
        <Switch
          checked={cookieState.analytics_cookies}
          onCheckedChange={(checked) => updateCookieSetting('analytics_cookies', checked)}
          disabled={isUpdating}
        />
      </div>

      {/* Marketing Cookies */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Target className="w-4 h-4 text-purple-500" />
          {showLabels && <span className="text-sm font-medium">Marketing Cookies</span>}
          {getStatusBadge(cookieState.marketing_cookies)}
        </div>
        <Switch
          checked={cookieState.marketing_cookies}
          onCheckedChange={(checked) => updateCookieSetting('marketing_cookies', checked)}
          disabled={isUpdating}
        />
      </div>

      {showLabels && (
        <p className="text-xs text-muted-foreground">
          Changes take effect immediately
        </p>
      )}
    </div>
  );
}

// Hook for accessing cookie state in other components
export function useCookieSettings() {
  const [cookieState, setCookieState] = useState<CookieState>({
    analytics_cookies: false,
    marketing_cookies: false
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchCookieState();
  }, []);

  const fetchCookieState = async () => {
    try {
      const response = await fetch('/api/gdpr/cookie-consent');
      
      if (response.ok) {
        const data = await response.json();
        const consent = data.consent || {};
        setCookieState({
          analytics_cookies: consent.analytics_cookies || false,
          marketing_cookies: consent.marketing_cookies || false
        });
      }
    } catch (error) {
      console.error('Failed to fetch cookie state:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const updateCookieSetting = async (type: 'analytics_cookies' | 'marketing_cookies', value: boolean) => {
    try {
      const newState = { ...cookieState, [type]: value };
      
      const response = await fetch('/api/gdpr/cookie-consent', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          necessary_cookies: true,
          analytics_cookies: newState.analytics_cookies,
          marketing_cookies: newState.marketing_cookies,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update cookie setting');
      }

      setCookieState(newState);
      return true;
    } catch (error) {
      console.error('Failed to update cookie setting:', error);
      return false;
    }
  };

  return {
    cookieState,
    isLoading,
    updateCookieSetting,
    refresh: fetchCookieState,
    canUseAnalytics: cookieState.analytics_cookies,
    canUseMarketing: cookieState.marketing_cookies
  };
}
