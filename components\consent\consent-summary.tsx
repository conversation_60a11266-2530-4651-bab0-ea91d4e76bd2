"use client";

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Shield, 
  Cookie, 
  Mail, 
  BarChart3, 
  Target, 
  CheckCircle, 
  XCircle,
  Settings,
  Loader2
} from 'lucide-react';
import Link from 'next/link';

interface ConsentSummaryData {
  gdprConsents: {
    marketing_emails: boolean;
  };
  cookieConsents: {
    analytics_cookies: boolean;
    marketing_cookies: boolean;
  };
  lastUpdated: string | null;
}

export function ConsentSummary() {
  const [consentData, setConsentData] = useState<ConsentSummaryData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchConsentSummary();
  }, []);

  const fetchConsentSummary = async () => {
    try {
      const [gdprResponse, cookieResponse] = await Promise.all([
        fetch('/api/gdpr/consent'),
        fetch('/api/gdpr/cookie-consent')
      ]);

      if (gdprResponse.ok && cookieResponse.ok) {
        const [gdprData, cookieData] = await Promise.all([
          gdprResponse.json(),
          cookieResponse.json()
        ]);

        setConsentData({
          gdprConsents: {
            marketing_emails: gdprData.consents?.marketing_emails?.given || false
          },
          cookieConsents: {
            analytics_cookies: cookieData.consent?.analytics_cookies || false,
            marketing_cookies: cookieData.consent?.marketing_cookies || false
          },
          lastUpdated: cookieData.consent?.consent_date || gdprData.consents?.marketing_emails?.date || null
        });
      }
    } catch (error) {
      console.error('Failed to fetch consent summary:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <Loader2 className="w-6 h-6 animate-spin" />
            <span className="ml-2">Loading consent status...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!consentData) {
    return null;
  }

  const getStatusBadge = (granted: boolean) => {
    return granted ? (
      <Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
        <CheckCircle className="w-3 h-3 mr-1" />
        Active
      </Badge>
    ) : (
      <Badge variant="outline" className="text-gray-600">
        <XCircle className="w-3 h-3 mr-1" />
        Disabled
      </Badge>
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Shield className="w-5 h-5 text-blue-500" />
            Privacy & Consent Status
          </div>
          <Link href="/privacy/consent">
            <Button variant="outline" size="sm">
              <Settings className="w-4 h-4 mr-2" />
              Manage
            </Button>
          </Link>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* GDPR Consents */}
        <div>
          <h4 className="font-medium mb-2 flex items-center gap-2">
            <Shield className="w-4 h-4" />
            Data Processing
          </h4>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Mail className="w-3 h-3 text-muted-foreground" />
                <span className="text-sm">Marketing Emails</span>
              </div>
              {getStatusBadge(consentData.gdprConsents.marketing_emails)}
            </div>
          </div>
        </div>

        {/* Cookie Consents */}
        <div>
          <h4 className="font-medium mb-2 flex items-center gap-2">
            <Cookie className="w-4 h-4" />
            Cookie Preferences
          </h4>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <BarChart3 className="w-3 h-3 text-muted-foreground" />
                <span className="text-sm">Analytics Cookies</span>
              </div>
              {getStatusBadge(consentData.cookieConsents.analytics_cookies)}
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Target className="w-3 h-3 text-muted-foreground" />
                <span className="text-sm">Marketing Cookies</span>
              </div>
              {getStatusBadge(consentData.cookieConsents.marketing_cookies)}
            </div>
          </div>
        </div>

        {/* Last Updated */}
        {consentData.lastUpdated && (
          <div className="pt-2 border-t">
            <p className="text-xs text-muted-foreground">
              Last updated: {new Date(consentData.lastUpdated).toLocaleDateString()}
            </p>
          </div>
        )}

        {/* Quick Actions */}
        <div className="pt-2 border-t">
          <div className="flex gap-2">
            <Link href="/privacy/consent" className="flex-1">
              <Button variant="outline" size="sm" className="w-full">
                Update Preferences
              </Button>
            </Link>
            <Link href="/gdpr" className="flex-1">
              <Button variant="outline" size="sm" className="w-full">
                GDPR Rights
              </Button>
            </Link>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
