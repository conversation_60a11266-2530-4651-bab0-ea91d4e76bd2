"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Clock, 
  Database, 
  Shield, 
  Info, 
  Calendar,
  Trash2,
  Archive,
  UserX,
  CheckCircle,
  AlertTriangle,
  Loader2,
  Eye,
  Download
} from 'lucide-react';
import { toast } from 'sonner';

interface UserDataRetention {
  dataType: string;
  description: string;
  retentionPeriod: string;
  retentionDays: number;
  legalBasis: string;
  deletionMethod: 'hard_delete' | 'anonymize' | 'archive';
  recordCount: number;
  nextCleanupDate: string | null;
  status: 'active' | 'pending_cleanup' | 'archived';
  lastUpdated: string;
}

interface DataRetentionSummary {
  totalDataTypes: number;
  activeRetentions: number;
  pendingCleanups: number;
  oldestData: string | null;
  nextScheduledCleanup: string | null;
}

export function DataRetentionDisplay() {
  const [retentionData, setRetentionData] = useState<UserDataRetention[]>([]);
  const [summary, setSummary] = useState<DataRetentionSummary | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedDataType, setSelectedDataType] = useState<UserDataRetention | null>(null);

  useEffect(() => {
    fetchUserRetentionData();
  }, []);

  const fetchUserRetentionData = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/account/data-retention');
      
      if (!response.ok) {
        if (response.status === 401) {
          toast.error('Please sign in to view data retention information');
          return;
        }
        throw new Error('Failed to fetch data retention information');
      }

      const data = await response.json();
      setRetentionData(data.retentionData || []);
      setSummary(data.summary || null);
    } catch (error: any) {
      toast.error(error.message || 'Failed to fetch data retention information');
    } finally {
      setIsLoading(false);
    }
  };

  const getRetentionBadge = (status: string) => {
    switch (status) {
      case 'active':
        return (
          <Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
            <CheckCircle className="w-3 h-3 mr-1" />
            Active
          </Badge>
        );
      case 'pending_cleanup':
        return (
          <Badge variant="destructive" className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
            <AlertTriangle className="w-3 h-3 mr-1" />
            Pending Cleanup
          </Badge>
        );
      case 'archived':
        return (
          <Badge variant="outline" className="text-gray-600">
            <Archive className="w-3 h-3 mr-1" />
            Archived
          </Badge>
        );
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getDeletionMethodIcon = (method: string) => {
    switch (method) {
      case 'hard_delete':
        return <Trash2 className="w-4 h-4 text-red-500" />;
      case 'anonymize':
        return <UserX className="w-4 h-4 text-orange-500" />;
      case 'archive':
        return <Archive className="w-4 h-4 text-blue-500" />;
      default:
        return <Database className="w-4 h-4 text-gray-500" />;
    }
  };

  const getDeletionMethodDescription = (method: string) => {
    switch (method) {
      case 'hard_delete':
        return 'Permanently deleted from our systems';
      case 'anonymize':
        return 'Personal identifiers removed, statistical data retained';
      case 'archive':
        return 'Moved to secure archive storage';
      default:
        return 'Processing method not specified';
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not scheduled';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const exportRetentionReport = async () => {
    try {
      const response = await fetch('/api/account/data-retention?format=export');
      
      if (!response.ok) {
        throw new Error('Failed to export retention report');
      }

      const data = await response.json();
      
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `data-retention-report-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast.success('Data retention report exported successfully');
    } catch (error: any) {
      toast.error('Failed to export retention report');
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <Loader2 className="w-6 h-6 animate-spin" />
            <span className="ml-2">Loading data retention information...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Card */}
      {summary && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="w-5 h-5 text-blue-500" />
              Data Retention Overview
            </CardTitle>
            <CardDescription>
              How we manage and retain your personal data in compliance with privacy regulations
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{summary.totalDataTypes}</div>
                <p className="text-sm text-muted-foreground">Data Types</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{summary.activeRetentions}</div>
                <p className="text-sm text-muted-foreground">Active Retentions</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">{summary.pendingCleanups}</div>
                <p className="text-sm text-muted-foreground">Pending Cleanups</p>
              </div>
              <div className="text-center">
                <div className="text-sm font-medium">{formatDate(summary.nextScheduledCleanup)}</div>
                <p className="text-sm text-muted-foreground">Next Cleanup</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Data Retention Policies */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Database className="w-5 h-5 text-green-500" />
              Your Data Retention Schedule
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={exportRetentionReport}
              >
                <Download className="w-4 h-4 mr-2" />
                Export Report
              </Button>
            </div>
          </CardTitle>
          <CardDescription>
            Detailed information about how long we keep different types of your data
          </CardDescription>
        </CardHeader>
        <CardContent>
          {retentionData.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Database className="w-8 h-8 mx-auto mb-2" />
              <p>No data retention information available</p>
            </div>
          ) : (
            <div className="space-y-4">
              {retentionData.map((item, index) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        {getDeletionMethodIcon(item.deletionMethod)}
                        <h3 className="font-semibold">{item.dataType}</h3>
                        {getRetentionBadge(item.status)}
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">
                        {item.description}
                      </p>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <span className="flex items-center gap-1">
                          <Calendar className="w-3 h-3" />
                          Retention: {item.retentionPeriod}
                        </span>
                        <span className="flex items-center gap-1">
                          <Database className="w-3 h-3" />
                          Records: {item.recordCount}
                        </span>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedDataType(item)}
                    >
                      <Eye className="w-4 h-4 mr-2" />
                      Details
                    </Button>
                  </div>

                  <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-3 text-sm">
                    <div>
                      <label className="font-medium">Legal Basis</label>
                      <p className="text-muted-foreground">{item.legalBasis}</p>
                    </div>
                    <div>
                      <label className="font-medium">Deletion Method</label>
                      <p className="text-muted-foreground">{getDeletionMethodDescription(item.deletionMethod)}</p>
                    </div>
                    <div>
                      <label className="font-medium">Next Cleanup</label>
                      <p className="text-muted-foreground">{formatDate(item.nextCleanupDate)}</p>
                    </div>
                  </div>

                  <div className="mt-3 pt-3 border-t text-xs text-muted-foreground">
                    Last updated: {formatDate(item.lastUpdated)}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Information Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="w-5 h-5 text-blue-500" />
            About Data Retention
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
              Why We Retain Data
            </h4>
            <p className="text-sm text-blue-800 dark:text-blue-200">
              We retain your data only as long as necessary for the purposes it was collected, 
              in compliance with GDPR Article 5(1)(e) - Storage Limitation Principle.
            </p>
          </div>

          <div className="grid gap-4 md:grid-cols-3">
            <div className="flex items-start gap-3">
              <Trash2 className="w-5 h-5 text-red-500 mt-0.5" />
              <div>
                <h4 className="font-medium mb-1">Hard Delete</h4>
                <p className="text-sm text-muted-foreground">
                  Data is permanently removed from all systems and cannot be recovered.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <UserX className="w-5 h-5 text-orange-500 mt-0.5" />
              <div>
                <h4 className="font-medium mb-1">Anonymization</h4>
                <p className="text-sm text-muted-foreground">
                  Personal identifiers are removed while preserving statistical value.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <Archive className="w-5 h-5 text-blue-500 mt-0.5" />
              <div>
                <h4 className="font-medium mb-1">Archive</h4>
                <p className="text-sm text-muted-foreground">
                  Data is moved to secure long-term storage for legal compliance.
                </p>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
            <h4 className="font-medium mb-2">Your Rights</h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• Request deletion of your data before the retention period expires</li>
              <li>• Export your data retention schedule and policies</li>
              <li>• Receive notifications before scheduled data cleanup</li>
              <li>• Object to processing based on legitimate interests</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* Data Type Detail Modal */}
      {selectedDataType && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-900 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold flex items-center gap-2">
                  {getDeletionMethodIcon(selectedDataType.deletionMethod)}
                  {selectedDataType.dataType}
                </h2>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedDataType(null)}
                >
                  Close
                </Button>
              </div>

              <div className="space-y-6">
                {/* Status and Overview */}
                <div>
                  <h3 className="font-medium mb-3">Current Status</h3>
                  <div className="flex items-center gap-4 mb-3">
                    {getRetentionBadge(selectedDataType.status)}
                    <span className="text-sm text-muted-foreground">
                      {selectedDataType.recordCount} records in our system
                    </span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {selectedDataType.description}
                  </p>
                </div>

                {/* Retention Details */}
                <div>
                  <h3 className="font-medium mb-3">Retention Policy</h3>
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="p-3 border rounded-lg">
                      <div className="flex items-center gap-2 mb-1">
                        <Calendar className="w-4 h-4 text-blue-500" />
                        <label className="font-medium">Retention Period</label>
                      </div>
                      <p className="text-sm text-muted-foreground">{selectedDataType.retentionPeriod}</p>
                    </div>

                    <div className="p-3 border rounded-lg">
                      <div className="flex items-center gap-2 mb-1">
                        <Shield className="w-4 h-4 text-green-500" />
                        <label className="font-medium">Legal Basis</label>
                      </div>
                      <p className="text-sm text-muted-foreground">{selectedDataType.legalBasis}</p>
                    </div>
                  </div>
                </div>

                {/* Deletion Method */}
                <div>
                  <h3 className="font-medium mb-3">How Data is Processed</h3>
                  <div className="p-4 border rounded-lg bg-gray-50 dark:bg-gray-900">
                    <div className="flex items-start gap-3">
                      {getDeletionMethodIcon(selectedDataType.deletionMethod)}
                      <div>
                        <h4 className="font-medium mb-1 capitalize">
                          {selectedDataType.deletionMethod.replace('_', ' ')}
                        </h4>
                        <p className="text-sm text-muted-foreground">
                          {getDeletionMethodDescription(selectedDataType.deletionMethod)}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Timeline */}
                <div>
                  <h3 className="font-medium mb-3">Timeline</h3>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3 p-3 border rounded-lg">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <div>
                        <p className="font-medium text-sm">Data Last Updated</p>
                        <p className="text-sm text-muted-foreground">
                          {formatDate(selectedDataType.lastUpdated)}
                        </p>
                      </div>
                    </div>

                    {selectedDataType.nextCleanupDate && (
                      <div className="flex items-center gap-3 p-3 border rounded-lg">
                        <Clock className="w-4 h-4 text-orange-500" />
                        <div>
                          <p className="font-medium text-sm">Next Scheduled Cleanup</p>
                          <p className="text-sm text-muted-foreground">
                            {formatDate(selectedDataType.nextCleanupDate)}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Your Rights */}
                <div>
                  <h3 className="font-medium mb-3">Your Rights</h3>
                  <div className="bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                    <ul className="text-sm space-y-2">
                      <li className="flex items-start gap-2">
                        <CheckCircle className="w-4 h-4 text-blue-500 mt-0.5" />
                        <span>Request early deletion of this data type</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <CheckCircle className="w-4 h-4 text-blue-500 mt-0.5" />
                        <span>Export your data before it&apos;s processed</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <CheckCircle className="w-4 h-4 text-blue-500 mt-0.5" />
                        <span>Object to processing based on legitimate interests</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <CheckCircle className="w-4 h-4 text-blue-500 mt-0.5" />
                        <span>Receive notification before scheduled cleanup</span>
                      </li>
                    </ul>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex gap-2 pt-4 border-t">
                  <Button variant="outline" className="flex-1">
                    <Download className="w-4 h-4 mr-2" />
                    Export This Data
                  </Button>
                  <Button variant="outline" className="flex-1">
                    <Trash2 className="w-4 h-4 mr-2" />
                    Request Deletion
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
