import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { supabaseAdmin } from "@/lib/supabase";
import { DATA_RETENTION_POLICIES, getRetentionCutoffDate } from "@/lib/data-retention";

/**
 * GET /api/account/data-retention
 * 
 * Get user-specific data retention information
 * Shows how their personal data is managed and retained
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth.api.getSession({ headers: headers() });
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized - Please sign in" }, 
        { status: 401 }
      );
    }

    const userId = session.user.id;
    const { searchParams } = new URL(request.url);
    const format = searchParams.get('format'); // 'export' for downloadable report

    // Get user-specific data counts for each retention policy
    const userRetentionData = await Promise.all(
      DATA_RETENTION_POLICIES.map(async (policy) => {
        try {
          // Get user's data count for this table
          let query = supabaseAdmin
            .from(policy.table as any)
            .select('id', { count: 'exact', head: true });

          // Add user filter for tables that have user_id
          if (['sessions', 'accounts', 'subscriptions', 'one_time_purchases', 
               'verifications', 'user_consents', 'audit_logs', 'deletion_requests', 
               'cookie_consents'].includes(policy.table)) {
            query = query.eq('user_id', userId);
          } else if (policy.table === 'users') {
            query = query.eq('id', userId);
          }

          const { count, error } = await query;

          if (error && error.code !== 'PGRST116') { // Ignore "no rows" error
            console.error(`Error querying ${policy.table}:`, error);
          }

          // Calculate next cleanup date
          const cutoffDate = policy.retentionPeriodDays === -1 
            ? null 
            : getRetentionCutoffDate(policy);

          // Determine status
          let status: 'active' | 'pending_cleanup' | 'archived' = 'active';
          if (policy.retentionPeriodDays === -1) {
            status = 'archived';
          } else if (count && count > 0 && cutoffDate && new Date() > cutoffDate) {
            status = 'pending_cleanup';
          }

          // Get last updated date for user's data in this table
          let lastUpdated = new Date().toISOString();
          try {
            if (count && count > 0) {
              let updateQuery = supabaseAdmin
                .from(policy.table as any)
                .select('updated_at, created_at')
                .order('updated_at', { ascending: false })
                .limit(1);

              if (['sessions', 'accounts', 'subscriptions', 'one_time_purchases', 
                   'verifications', 'user_consents', 'audit_logs', 'deletion_requests', 
                   'cookie_consents'].includes(policy.table)) {
                updateQuery = updateQuery.eq('user_id', userId);
              } else if (policy.table === 'users') {
                updateQuery = updateQuery.eq('id', userId);
              }

              const { data: latestRecord } = await updateQuery;
              if (latestRecord && latestRecord[0]) {
                const record = latestRecord[0] as { updated_at?: string; created_at?: string };
                lastUpdated = record.updated_at || record.created_at || lastUpdated;
              }
            }
          } catch (updateError) {
            // Ignore errors when fetching update dates
          }

          return {
            dataType: policy.description,
            description: getDataTypeDescription(policy.table, policy.description),
            retentionPeriod: policy.retentionPeriodDays === -1 
              ? 'Permanent (never deleted)' 
              : `${policy.retentionPeriodDays} days`,
            retentionDays: policy.retentionPeriodDays,
            legalBasis: policy.legalBasis,
            deletionMethod: policy.deletionMethod,
            recordCount: count || 0,
            nextCleanupDate: cutoffDate ? cutoffDate.toISOString() : null,
            status,
            lastUpdated,
            tableName: policy.table
          };
        } catch (error) {
          console.error(`Error processing retention policy for ${policy.table}:`, error);
          return {
            dataType: policy.description,
            description: getDataTypeDescription(policy.table, policy.description),
            retentionPeriod: policy.retentionPeriodDays === -1 
              ? 'Permanent (never deleted)' 
              : `${policy.retentionPeriodDays} days`,
            retentionDays: policy.retentionPeriodDays,
            legalBasis: policy.legalBasis,
            deletionMethod: policy.deletionMethod,
            recordCount: 0,
            nextCleanupDate: null,
            status: 'active' as const,
            lastUpdated: new Date().toISOString(),
            tableName: policy.table
          };
        }
      })
    );

    // Filter out data types with no records for cleaner display
    const relevantRetentionData = userRetentionData.filter(item => item.recordCount > 0);

    // Calculate summary statistics
    const summary = {
      totalDataTypes: relevantRetentionData.length,
      activeRetentions: relevantRetentionData.filter(item => item.status === 'active').length,
      pendingCleanups: relevantRetentionData.filter(item => item.status === 'pending_cleanup').length,
      oldestData: relevantRetentionData.length > 0 
        ? relevantRetentionData.reduce((oldest, current) => 
            new Date(current.lastUpdated) < new Date(oldest.lastUpdated) ? current : oldest
          ).lastUpdated
        : null,
      nextScheduledCleanup: relevantRetentionData
        .filter(item => item.nextCleanupDate)
        .sort((a, b) => new Date(a.nextCleanupDate!).getTime() - new Date(b.nextCleanupDate!).getTime())[0]?.nextCleanupDate || null
    };

    // Log the data retention view for audit purposes
    await supabaseAdmin
      .from('audit_logs')
      .insert({
        user_id: userId,
        action: 'data_retention_viewed',
        resource_type: 'data_retention_info',
        details: {
          format: format || 'web',
          data_types_shown: relevantRetentionData.length,
          total_records: relevantRetentionData.reduce((sum, item) => sum + item.recordCount, 0)
        },
        ip_address: request.headers.get('x-forwarded-for') || 'unknown',
        user_agent: request.headers.get('user-agent') || 'unknown'
      });

    // Return export format if requested
    if (format === 'export') {
      return NextResponse.json({
        export_info: {
          export_date: new Date().toISOString(),
          user_id: userId,
          export_type: 'data_retention_report',
          gdpr_article: 'Article 5(1)(e) - Storage Limitation'
        },
        summary,
        retention_policies: userRetentionData,
        user_data_summary: {
          total_data_types: summary.totalDataTypes,
          total_records: relevantRetentionData.reduce((sum, item) => sum + item.recordCount, 0),
          oldest_data_date: summary.oldestData,
          next_cleanup_date: summary.nextScheduledCleanup
        }
      });
    }

    return NextResponse.json({
      success: true,
      summary,
      retentionData: relevantRetentionData
    });

  } catch (error: any) {
    console.error('Data retention fetch error:', error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * Get user-friendly description for data types
 */
function getDataTypeDescription(tableName: string, defaultDescription: string): string {
  const descriptions: Record<string, string> = {
    'users': 'Your account profile, settings, and basic information',
    'sessions': 'Login sessions and authentication tokens for security',
    'accounts': 'Connected social media and OAuth account information',
    'subscriptions': 'Your subscription history and billing information',
    'one_time_purchases': 'Records of one-time purchases and transactions',
    'verifications': 'Email verification tokens and account confirmation data',
    'user_consents': 'Your privacy preferences and consent choices',
    'audit_logs': 'Security logs of actions performed on your account',
    'deletion_requests': 'Records of any account deletion requests you\'ve made',
    'cookie_consents': 'Your cookie preferences and consent history'
  };

  return descriptions[tableName] || defaultDescription;
}
