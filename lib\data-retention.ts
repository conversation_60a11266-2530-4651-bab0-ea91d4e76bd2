/**
 * Data Retention Configuration and Management
 * 
 * GDPR Article 5(1)(e) - Storage limitation principle
 * Personal data shall be kept in a form which permits identification of data subjects 
 * for no longer than is necessary for the purposes for which the personal data are processed
 */

import { supabaseAdmin } from '@/lib/supabase';

export interface RetentionPolicy {
  table: string;
  retentionPeriodDays: number;
  dateColumn: string;
  description: string;
  legalBasis: string;
  deletionMethod: 'hard_delete' | 'anonymize' | 'archive';
  conditions?: string; // SQL WHERE conditions for selective retention
}

/**
 * Data Retention Policies Configuration
 * Based on legal requirements and business needs
 */
export const DATA_RETENTION_POLICIES: RetentionPolicy[] = [
  // Session data - short retention for security
  {
    table: 'sessions',
    retentionPeriodDays: 90,
    dateColumn: 'created_at',
    description: 'User session data',
    legalBasis: 'Security and fraud prevention',
    deletionMethod: 'hard_delete'
  },

  // Verification tokens - very short retention
  {
    table: 'verifications',
    retentionPeriodDays: 7,
    dateColumn: 'created_at',
    description: 'Email verification tokens',
    legalBasis: 'Account verification process',
    deletionMethod: 'hard_delete',
    conditions: "expires_at < NOW()" // Only delete expired tokens
  },

  // Audit logs - longer retention for compliance
  {
    table: 'audit_logs',
    retentionPeriodDays: 1095, // 3 years
    dateColumn: 'created_at',
    description: 'Security and compliance audit logs',
    legalBasis: 'Legal compliance and security monitoring',
    deletionMethod: 'anonymize' // Keep logs but remove personal identifiers
  },

  // Cookie consents - moderate retention
  {
    table: 'cookie_consents',
    retentionPeriodDays: 730, // 2 years
    dateColumn: 'created_at',
    description: 'Cookie consent records',
    legalBasis: 'GDPR consent documentation',
    deletionMethod: 'hard_delete',
    conditions: "user_id IS NOT NULL" // Keep anonymous consents longer
  },

  // User consents - longer retention for legal proof
  {
    table: 'user_consents',
    retentionPeriodDays: 1095, // 3 years
    dateColumn: 'created_at',
    description: 'GDPR consent records',
    legalBasis: 'Legal proof of consent',
    deletionMethod: 'anonymize' // Keep consent records but anonymize
  },

  // Deletion requests - permanent retention for compliance
  {
    table: 'deletion_requests',
    retentionPeriodDays: -1, // Never delete
    dateColumn: 'created_at',
    description: 'Account deletion requests',
    legalBasis: 'Legal compliance and audit trail',
    deletionMethod: 'archive'
  },

  // Inactive user accounts - 3 years after last activity
  {
    table: 'users',
    retentionPeriodDays: 1095, // 3 years
    dateColumn: 'updated_at',
    description: 'Inactive user accounts',
    legalBasis: 'Account maintenance and security',
    deletionMethod: 'hard_delete',
    conditions: "email_verified = false OR updated_at < NOW() - INTERVAL '1 year'" // Unverified or inactive accounts
  }
];

/**
 * Get retention policy for a specific table
 */
export function getRetentionPolicy(tableName: string): RetentionPolicy | undefined {
  return DATA_RETENTION_POLICIES.find(policy => policy.table === tableName);
}

/**
 * Calculate retention cutoff date for a policy
 */
export function getRetentionCutoffDate(policy: RetentionPolicy): Date {
  if (policy.retentionPeriodDays === -1) {
    // Never delete
    return new Date('1970-01-01');
  }
  
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - policy.retentionPeriodDays);
  return cutoffDate;
}

/**
 * Check if data retention cleanup is needed for a table
 */
export async function checkRetentionNeeded(policy: RetentionPolicy): Promise<{
  needed: boolean;
  recordCount: number;
  cutoffDate: Date;
}> {
  if (policy.retentionPeriodDays === -1) {
    return { needed: false, recordCount: 0, cutoffDate: new Date() };
  }

  const cutoffDate = getRetentionCutoffDate(policy);
  
  let query = supabaseAdmin
    .from(policy.table as any)
    .select('id', { count: 'exact', head: true })
    .lt(policy.dateColumn, cutoffDate.toISOString());

  // Apply additional conditions if specified
  if (policy.conditions) {
    // Note: In a production system, you'd want to parse and apply conditions more safely
    // For now, we'll document this as a limitation
    console.warn(`Custom conditions not applied for ${policy.table}: ${policy.conditions}`);
  }

  const { count, error } = await query;

  if (error) {
    console.error(`Error checking retention for ${policy.table}:`, error);
    return { needed: false, recordCount: 0, cutoffDate };
  }

  return {
    needed: (count || 0) > 0,
    recordCount: count || 0,
    cutoffDate
  };
}

/**
 * Get data retention status for all policies
 */
export async function getDataRetentionStatus(): Promise<{
  policies: (RetentionPolicy & {
    recordCount: number;
    cutoffDate: Date;
    cleanupNeeded: boolean;
  })[];
  totalRecordsToCleanup: number;
}> {
  const results = await Promise.all(
    DATA_RETENTION_POLICIES.map(async (policy) => {
      const status = await checkRetentionNeeded(policy);
      return {
        ...policy,
        recordCount: status.recordCount,
        cutoffDate: status.cutoffDate,
        cleanupNeeded: status.needed
      };
    })
  );

  const totalRecordsToCleanup = results.reduce(
    (total, result) => total + (result.cleanupNeeded ? result.recordCount : 0),
    0
  );

  return {
    policies: results,
    totalRecordsToCleanup
  };
}

/**
 * Anonymize data by removing personal identifiers
 */
export async function anonymizeData(tableName: string, recordIds: string[]): Promise<void> {
  const anonymizationMap: Record<string, any> = {
    audit_logs: {
      user_id: null,
      ip_address: null,
      user_agent: 'anonymized',
      details: { anonymized: true, original_action: 'redacted' }
    },
    user_consents: {
      user_id: null,
      ip_address: null,
      user_agent: 'anonymized'
    }
  };

  const updateData = anonymizationMap[tableName];
  if (!updateData) {
    throw new Error(`No anonymization mapping defined for table: ${tableName}`);
  }

  const { error } = await supabaseAdmin
    .from(tableName as any)
    .update(updateData)
    .in('id', recordIds);

  if (error) {
    throw new Error(`Failed to anonymize data in ${tableName}: ${error.message}`);
  }
}

/**
 * Execute data retention cleanup for a specific policy
 */
export async function executeRetentionCleanup(policy: RetentionPolicy): Promise<{
  success: boolean;
  recordsProcessed: number;
  error?: string;
}> {
  try {
    if (policy.retentionPeriodDays === -1) {
      return { success: true, recordsProcessed: 0 };
    }

    const cutoffDate = getRetentionCutoffDate(policy);
    
    // First, get the records that will be affected
    let selectQuery = supabaseAdmin
      .from(policy.table as any)
      .select('id')
      .lt(policy.dateColumn, cutoffDate.toISOString());

    const { data: recordsToProcess, error: selectError } = await selectQuery;

    if (selectError) {
      throw new Error(`Failed to select records: ${selectError.message}`);
    }

    if (!recordsToProcess || recordsToProcess.length === 0) {
      return { success: true, recordsProcessed: 0 };
    }

    const recordIds = recordsToProcess.map(record => (record as any).id);

    // Execute the appropriate deletion method
    switch (policy.deletionMethod) {
      case 'hard_delete':
        const { error: deleteError } = await supabaseAdmin
          .from(policy.table as any)
          .delete()
          .in('id', recordIds);

        if (deleteError) {
          throw new Error(`Failed to delete records: ${deleteError.message}`);
        }
        break;

      case 'anonymize':
        await anonymizeData(policy.table, recordIds);
        break;

      case 'archive':
        // For now, we'll just log that archiving is needed
        // In a production system, you'd move data to an archive table
        console.log(`Archiving needed for ${recordIds.length} records in ${policy.table}`);
        break;
    }

    // Log the retention cleanup action
    await supabaseAdmin
      .from('audit_logs')
      .insert({
        user_id: null, // System action
        action: 'data_retention_cleanup',
        resource_type: policy.table,
        details: {
          policy_description: policy.description,
          retention_period_days: policy.retentionPeriodDays,
          deletion_method: policy.deletionMethod,
          records_processed: recordIds.length,
          cutoff_date: cutoffDate.toISOString(),
          legal_basis: policy.legalBasis
        }
      });

    return {
      success: true,
      recordsProcessed: recordIds.length
    };

  } catch (error: any) {
    return {
      success: false,
      recordsProcessed: 0,
      error: error.message
    };
  }
}
