"use client";

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Clock, 
  Database, 
  Trash2, 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  Loader2,
  Eye,
  Play
} from 'lucide-react';
import { toast } from 'sonner';

interface RetentionPolicy {
  table: string;
  description: string;
  retentionPeriodDays: number;
  retentionPeriodHuman: string;
  legalBasis: string;
  deletionMethod: 'hard_delete' | 'anonymize' | 'archive';
  recordCount: number;
  cutoffDate: string;
  cleanupNeeded: boolean;
}

interface RetentionStatus {
  summary: {
    totalPolicies: number;
    policiesNeedingCleanup: number;
    totalRecordsToCleanup: number;
    lastChecked: string;
  };
  policies: RetentionPolicy[];
}

export function DataRetentionDashboard() {
  const [retentionStatus, setRetentionStatus] = useState<RetentionStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRunningCleanup, setIsRunningCleanup] = useState(false);
  const [selectedTables, setSelectedTables] = useState<string[]>([]);

  useEffect(() => {
    fetchRetentionStatus();
  }, []);

  const fetchRetentionStatus = async () => {
    try {
      const response = await fetch('/api/admin/data-retention');
      
      if (!response.ok) {
        if (response.status === 401) {
          toast.error('Admin access required');
          return;
        }
        throw new Error('Failed to fetch retention status');
      }

      const data = await response.json();
      setRetentionStatus(data);
    } catch (error: any) {
      toast.error(error.message || 'Failed to fetch retention status');
    } finally {
      setIsLoading(false);
    }
  };

  const runCleanup = async (dryRun: boolean = true) => {
    if (selectedTables.length === 0) {
      toast.error('Please select at least one table to clean up');
      return;
    }

    setIsRunningCleanup(true);
    try {
      const response = await fetch('/api/admin/data-retention', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tables: selectedTables,
          dryRun,
          force: false
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to run cleanup');
      }

      const data = await response.json();
      
      if (dryRun) {
        toast.success(`Dry run completed. Would process ${data.summary.totalRecordsProcessed} records.`);
      } else {
        toast.success(`Cleanup completed. Processed ${data.summary.totalRecordsProcessed} records.`);
        // Refresh status after actual cleanup
        await fetchRetentionStatus();
      }

      console.log('Cleanup results:', data);
    } catch (error: any) {
      toast.error(error.message || 'Cleanup failed');
    } finally {
      setIsRunningCleanup(false);
    }
  };

  const toggleTableSelection = (tableName: string) => {
    setSelectedTables(prev => 
      prev.includes(tableName)
        ? prev.filter(t => t !== tableName)
        : [...prev, tableName]
    );
  };

  const selectAllCleanupNeeded = () => {
    if (!retentionStatus) return;
    
    const tablesNeedingCleanup = retentionStatus.policies
      .filter(p => p.cleanupNeeded)
      .map(p => p.table);
    
    setSelectedTables(tablesNeedingCleanup);
  };

  const getDeletionMethodBadge = (method: string) => {
    switch (method) {
      case 'hard_delete':
        return <Badge variant="destructive">Hard Delete</Badge>;
      case 'anonymize':
        return <Badge variant="secondary">Anonymize</Badge>;
      case 'archive':
        return <Badge variant="outline">Archive</Badge>;
      default:
        return <Badge>{method}</Badge>;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="w-8 h-8 animate-spin" />
        <span className="ml-2">Loading retention status...</span>
      </div>
    );
  }

  if (!retentionStatus) {
    return (
      <div className="text-center p-8">
        <AlertTriangle className="w-12 h-12 mx-auto mb-4 text-yellow-500" />
        <p>Failed to load data retention status</p>
        <Button onClick={fetchRetentionStatus} className="mt-4">
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Policies</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{retentionStatus.summary.totalPolicies}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Need Cleanup</CardTitle>
            <AlertTriangle className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{retentionStatus.summary.policiesNeedingCleanup}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Records to Clean</CardTitle>
            <Trash2 className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{retentionStatus.summary.totalRecordsToCleanup.toLocaleString()}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Last Checked</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-sm">
              {new Date(retentionStatus.summary.lastChecked).toLocaleString()}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-4">
        <Button onClick={fetchRetentionStatus} variant="outline">
          <Database className="w-4 h-4 mr-2" />
          Refresh Status
        </Button>
        
        <Button 
          onClick={selectAllCleanupNeeded} 
          variant="outline"
          disabled={retentionStatus.summary.policiesNeedingCleanup === 0}
        >
          Select All Needing Cleanup
        </Button>

        <Button 
          onClick={() => runCleanup(true)} 
          variant="outline"
          disabled={selectedTables.length === 0 || isRunningCleanup}
        >
          {isRunningCleanup ? (
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          ) : (
            <Eye className="w-4 h-4 mr-2" />
          )}
          Dry Run
        </Button>

        <Button 
          onClick={() => runCleanup(false)} 
          variant="destructive"
          disabled={selectedTables.length === 0 || isRunningCleanup}
        >
          {isRunningCleanup ? (
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          ) : (
            <Play className="w-4 h-4 mr-2" />
          )}
          Run Cleanup
        </Button>
      </div>

      {/* Policies Table */}
      <Card>
        <CardHeader>
          <CardTitle>Data Retention Policies</CardTitle>
          <CardDescription>
            Current retention policies and cleanup status for each data type
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {retentionStatus.policies.map((policy) => (
              <div 
                key={policy.table}
                className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                  selectedTables.includes(policy.table) 
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-950' 
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => toggleTableSelection(policy.table)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="font-semibold">{policy.table}</h3>
                      {getDeletionMethodBadge(policy.deletionMethod)}
                      {policy.cleanupNeeded ? (
                        <Badge variant="destructive">
                          <AlertTriangle className="w-3 h-3 mr-1" />
                          Cleanup Needed
                        </Badge>
                      ) : (
                        <Badge variant="secondary">
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Up to Date
                        </Badge>
                      )}
                    </div>
                    
                    <p className="text-sm text-muted-foreground mb-2">
                      {policy.description}
                    </p>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="font-medium">Retention:</span> {policy.retentionPeriodHuman}
                      </div>
                      <div>
                        <span className="font-medium">Records:</span> {policy.recordCount.toLocaleString()}
                      </div>
                      <div>
                        <span className="font-medium">Cutoff:</span> {new Date(policy.cutoffDate).toLocaleDateString()}
                      </div>
                      <div>
                        <span className="font-medium">Legal Basis:</span> {policy.legalBasis}
                      </div>
                    </div>
                  </div>
                  
                  <div className="ml-4">
                    <input
                      type="checkbox"
                      checked={selectedTables.includes(policy.table)}
                      onChange={() => toggleTableSelection(policy.table)}
                      className="w-4 h-4"
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
