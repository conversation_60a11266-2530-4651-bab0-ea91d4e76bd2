import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { supabaseAdmin } from "@/lib/supabase";

/**
 * POST /api/gdpr/rectification
 * 
 * GDPR Article 16 - Right to Rectification
 * Handle user requests to correct or update their personal data
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth.api.getSession({ headers: headers() });
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" }, 
        { status: 401 }
      );
    }

    const body = await request.json();
    const { 
      request_type, 
      current_data, 
      requested_data, 
      reason,
      field_name 
    } = body;

    // Validate required fields
    if (!request_type || !field_name || !requested_data) {
      return NextResponse.json(
        { error: "Missing required fields: request_type, field_name, requested_data" },
        { status: 400 }
      );
    }

    const userId = session.user.id;
    const clientIP = request.headers.get('x-forwarded-for') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Create rectification request record
    const rectificationRequest = {
      user_id: userId,
      request_type: 'rectification',
      status: 'pending',
      details: {
        field_name,
        current_data,
        requested_data,
        reason: reason || 'User requested data correction',
        request_type
      },
      ip_address: clientIP,
      user_agent: userAgent
    };

    // Insert into audit_logs table (we'll use this to track rectification requests)
    const { data, error } = await supabaseAdmin
      .from('audit_logs')
      .insert({
        user_id: userId,
        action: 'rectification_request',
        resource_type: 'user_data',
        resource_id: field_name,
        details: rectificationRequest.details,
        ip_address: clientIP,
        user_agent: userAgent
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    // For certain fields, we can auto-approve and update immediately
    const autoApprovableFields = ['name', 'email'];
    
    if (autoApprovableFields.includes(field_name) && request_type === 'profile_update') {
      try {
        // Update the user profile directly
        const updateData: any = {};
        updateData[field_name] = requested_data;
        
        const { error: updateError } = await supabaseAdmin
          .from('users')
          .update(updateData)
          .eq('id', userId);

        if (!updateError) {
          // Log the successful update
          await supabaseAdmin
            .from('audit_logs')
            .insert({
              user_id: userId,
              action: 'rectification_completed',
              resource_type: 'user_profile',
              resource_id: field_name,
              details: {
                field_updated: field_name,
                old_value: current_data,
                new_value: requested_data,
                auto_approved: true
              },
              ip_address: clientIP,
              user_agent: userAgent
            });

          return NextResponse.json({
            success: true,
            message: "Data rectification completed successfully",
            request_id: data.id,
            status: 'completed',
            auto_approved: true
          });
        }
      } catch (updateError) {
        console.error('Auto-update failed:', updateError);
        // Continue with manual review process
      }
    }

    console.log(`Rectification request created for user: ${userId}, field: ${field_name}`);

    return NextResponse.json({
      success: true,
      message: "Rectification request submitted successfully. We will review and process your request within 30 days.",
      request_id: data.id,
      status: 'pending',
      estimated_completion: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days from now
    });

  } catch (error: any) {
    console.error('Rectification request error:', error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * GET /api/gdpr/rectification
 * 
 * Get user's rectification request history
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth.api.getSession({ headers: headers() });
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" }, 
        { status: 401 }
      );
    }

    const userId = session.user.id;

    // Get all rectification requests for the user
    const { data: requests, error } = await supabaseAdmin
      .from('audit_logs')
      .select('*')
      .eq('user_id', userId)
      .in('action', ['rectification_request', 'rectification_completed'])
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    return NextResponse.json({
      success: true,
      requests: requests || []
    });

  } catch (error: any) {
    console.error('Get rectification requests error:', error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}
