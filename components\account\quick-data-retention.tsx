"use client";

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Clock, 
  AlertTriangle, 
  CheckCircle,
  Loader2,
  Calendar,
  Database
} from 'lucide-react';
import Link from 'next/link';

interface QuickRetentionData {
  pendingCleanups: number;
  nextCleanupDate: string | null;
  totalDataTypes: number;
}

export function QuickDataRetention() {
  const [data, setData] = useState<QuickRetentionData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchQuickData();
  }, []);

  const fetchQuickData = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/account/data-retention');
      
      if (!response.ok) {
        if (response.status === 401) {
          return; // User not authenticated
        }
        throw new Error('Failed to fetch retention data');
      }

      const result = await response.json();
      setData({
        pendingCleanups: result.summary?.pendingCleanups || 0,
        nextCleanupDate: result.summary?.nextScheduledCleanup || null,
        totalDataTypes: result.summary?.totalDataTypes || 0
      });
    } catch (error) {
      console.error('Failed to fetch quick retention data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'None scheduled';
    const date = new Date(dateString);
    const now = new Date();
    const diffDays = Math.ceil((date.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffDays <= 0) return 'Today';
    if (diffDays === 1) return 'Tomorrow';
    if (diffDays <= 7) return `In ${diffDays} days`;
    
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  if (isLoading) {
    return (
      <div className="p-3 border rounded-lg bg-gray-50 dark:bg-gray-900">
        <div className="flex items-center gap-2">
          <Loader2 className="w-4 h-4 animate-spin" />
          <span className="text-sm">Loading retention info...</span>
        </div>
      </div>
    );
  }

  if (!data) {
    return null; // Don't show if no data
  }

  return (
    <div className="p-3 border rounded-lg bg-gray-50 dark:bg-gray-900">
      <div className="flex items-center justify-between mb-2">
        <h4 className="text-sm font-medium text-neutral-900 dark:text-neutral-200 flex items-center gap-2">
          <Clock className="w-4 h-4 text-blue-500" />
          Data Retention
        </h4>
        <Link href="/account/data-retention">
          <Button variant="ghost" size="sm" className="h-6 px-2 text-xs">
            View All
          </Button>
        </Link>
      </div>

      <div className="space-y-2">
        {/* Status */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">Status</span>
          {data.pendingCleanups > 0 ? (
            <Badge variant="destructive" className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
              <AlertTriangle className="w-3 h-3 mr-1" />
              {data.pendingCleanups} Pending
            </Badge>
          ) : (
            <Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
              <CheckCircle className="w-3 h-3 mr-1" />
              Up to Date
            </Badge>
          )}
        </div>

        {/* Data Types */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">Data Types</span>
          <div className="flex items-center gap-1">
            <Database className="w-3 h-3 text-muted-foreground" />
            <span className="text-sm font-medium">{data.totalDataTypes}</span>
          </div>
        </div>

        {/* Next Cleanup */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">Next Cleanup</span>
          <div className="flex items-center gap-1">
            <Calendar className="w-3 h-3 text-muted-foreground" />
            <span className="text-sm font-medium">{formatDate(data.nextCleanupDate)}</span>
          </div>
        </div>
      </div>

      {/* Info */}
      <div className="mt-2 pt-2 border-t">
        <p className="text-xs text-muted-foreground">
          We automatically manage your data lifecycle according to GDPR requirements.
        </p>
      </div>
    </div>
  );
}
