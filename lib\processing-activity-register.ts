/**
 * Processing Activity Register (GDPR Article 30)
 * 
 * This module implements a comprehensive processing activity register as required by
 * GDPR Article 30. It tracks all data processing activities, their purposes, legal bases,
 * data categories, recipients, and retention periods.
 * 
 * Key Features:
 * - Automatic activity logging for all data processing operations
 * - GDPR Article 30 compliance with required record fields
 * - Processing purpose categorization and legal basis tracking
 * - Data subject category and personal data category classification
 * - Recipient and third-party transfer documentation
 * - Retention period tracking and compliance monitoring
 */

import { supabaseAdmin } from '@/lib/supabase';

/**
 * GDPR Article 30 Processing Activity Record
 * Contains all required fields for processing activity documentation
 */
export interface ProcessingActivity {
  id?: string;
  activity_name: string;                    // Name and description of processing activity
  controller_name: string;                  // Name of data controller
  controller_contact: string;               // Contact details of controller
  dpo_contact?: string;                     // Data Protection Officer contact (if applicable)
  purposes: string[];                       // Purposes of processing
  legal_bases: LegalBasis[];               // Legal bases for processing
  data_subject_categories: DataSubjectCategory[]; // Categories of data subjects
  personal_data_categories: PersonalDataCategory[]; // Categories of personal data
  recipients?: string[];                    // Recipients or categories of recipients
  third_country_transfers?: ThirdCountryTransfer[]; // International transfers
  retention_periods: RetentionPeriod[];     // Data retention information
  security_measures: string[];              // Technical and organizational measures
  created_at?: string;
  updated_at?: string;
  user_id?: string;                        // User who initiated the processing (if applicable)
  system_generated: boolean;               // Whether this was auto-generated
}

/**
 * Legal bases for processing under GDPR Article 6
 */
export enum LegalBasis {
  CONSENT = 'consent',                     // Article 6(1)(a) - Consent
  CONTRACT = 'contract',                   // Article 6(1)(b) - Contract performance
  LEGAL_OBLIGATION = 'legal_obligation',   // Article 6(1)(c) - Legal obligation
  VITAL_INTERESTS = 'vital_interests',     // Article 6(1)(d) - Vital interests
  PUBLIC_TASK = 'public_task',            // Article 6(1)(e) - Public task
  LEGITIMATE_INTERESTS = 'legitimate_interests' // Article 6(1)(f) - Legitimate interests
}

/**
 * Categories of data subjects
 */
export enum DataSubjectCategory {
  CUSTOMERS = 'customers',
  EMPLOYEES = 'employees',
  PROSPECTS = 'prospects',
  WEBSITE_VISITORS = 'website_visitors',
  SUBSCRIBERS = 'subscribers',
  FORMER_CUSTOMERS = 'former_customers'
}

/**
 * Categories of personal data being processed
 */
export enum PersonalDataCategory {
  IDENTIFICATION_DATA = 'identification_data',     // Name, email, phone
  CONTACT_DATA = 'contact_data',                  // Address, communication preferences
  FINANCIAL_DATA = 'financial_data',              // Payment information, billing
  TECHNICAL_DATA = 'technical_data',              // IP address, cookies, device info
  USAGE_DATA = 'usage_data',                      // How services are used
  MARKETING_DATA = 'marketing_data',              // Preferences, communication history
  AUTHENTICATION_DATA = 'authentication_data',    // Passwords, security tokens
  BEHAVIORAL_DATA = 'behavioral_data'             // User behavior patterns
}

/**
 * Third country transfer information
 */
export interface ThirdCountryTransfer {
  country: string;                         // Destination country
  adequacy_decision: boolean;              // Whether country has adequacy decision
  safeguards: string[];                    // Appropriate safeguards in place
  recipient: string;                       // Name of recipient
  purpose: string;                         // Purpose of transfer
}

/**
 * Retention period information
 */
export interface RetentionPeriod {
  data_category: PersonalDataCategory;
  retention_period: string;                // Human-readable retention period
  retention_days: number;                  // Retention period in days
  legal_basis: string;                     // Legal basis for retention period
  deletion_method: 'hard_delete' | 'anonymize' | 'archive';
}

/**
 * Predefined processing activities for common SaaS operations
 */
export const STANDARD_PROCESSING_ACTIVITIES: Omit<ProcessingActivity, 'id' | 'created_at' | 'updated_at'>[] = [
  {
    activity_name: 'User Account Management',
    controller_name: 'GenAForm SaaS Platform',
    controller_contact: '<EMAIL>',
    purposes: ['Account creation and management', 'User authentication', 'Service provision'],
    legal_bases: [LegalBasis.CONTRACT, LegalBasis.LEGITIMATE_INTERESTS],
    data_subject_categories: [DataSubjectCategory.CUSTOMERS],
    personal_data_categories: [
      PersonalDataCategory.IDENTIFICATION_DATA,
      PersonalDataCategory.CONTACT_DATA,
      PersonalDataCategory.AUTHENTICATION_DATA
    ],
    recipients: ['Internal staff', 'Technical service providers'],
    retention_periods: [
      {
        data_category: PersonalDataCategory.IDENTIFICATION_DATA,
        retention_period: '3 years after account closure',
        retention_days: 1095,
        legal_basis: 'Contract and legal obligations',
        deletion_method: 'hard_delete'
      }
    ],
    security_measures: [
      'Encryption at rest and in transit',
      'Access controls and authentication',
      'Regular security audits',
      'Data backup and recovery procedures'
    ],
    system_generated: true
  },
  
  {
    activity_name: 'Payment Processing',
    controller_name: 'GenAForm SaaS Platform',
    controller_contact: '<EMAIL>',
    purposes: ['Payment processing', 'Billing and invoicing', 'Fraud prevention'],
    legal_bases: [LegalBasis.CONTRACT, LegalBasis.LEGAL_OBLIGATION],
    data_subject_categories: [DataSubjectCategory.CUSTOMERS],
    personal_data_categories: [
      PersonalDataCategory.IDENTIFICATION_DATA,
      PersonalDataCategory.FINANCIAL_DATA,
      PersonalDataCategory.CONTACT_DATA
    ],
    recipients: ['Payment processors (Polar.sh)', 'Financial institutions', 'Tax authorities'],
    third_country_transfers: [
      {
        country: 'United States',
        adequacy_decision: false,
        safeguards: ['Standard Contractual Clauses', 'Privacy Shield successor framework'],
        recipient: 'Polar.sh Payment Processor',
        purpose: 'Payment processing and fraud prevention'
      }
    ],
    retention_periods: [
      {
        data_category: PersonalDataCategory.FINANCIAL_DATA,
        retention_period: '7 years for tax compliance',
        retention_days: 2555,
        legal_basis: 'Legal obligation (tax law)',
        deletion_method: 'archive'
      }
    ],
    security_measures: [
      'PCI DSS compliance',
      'Tokenization of payment data',
      'Encrypted data transmission',
      'Regular security assessments'
    ],
    system_generated: true
  },

  {
    activity_name: 'Marketing Communications',
    controller_name: 'GenAForm SaaS Platform',
    controller_contact: '<EMAIL>',
    purposes: ['Direct marketing', 'Product updates', 'Customer engagement'],
    legal_bases: [LegalBasis.CONSENT, LegalBasis.LEGITIMATE_INTERESTS],
    data_subject_categories: [DataSubjectCategory.CUSTOMERS, DataSubjectCategory.PROSPECTS],
    personal_data_categories: [
      PersonalDataCategory.IDENTIFICATION_DATA,
      PersonalDataCategory.CONTACT_DATA,
      PersonalDataCategory.MARKETING_DATA,
      PersonalDataCategory.BEHAVIORAL_DATA
    ],
    recipients: ['Email service providers', 'Marketing automation platforms'],
    retention_periods: [
      {
        data_category: PersonalDataCategory.MARKETING_DATA,
        retention_period: '2 years after consent withdrawal',
        retention_days: 730,
        legal_basis: 'Consent management and compliance',
        deletion_method: 'hard_delete'
      }
    ],
    security_measures: [
      'Consent management systems',
      'Opt-out mechanisms',
      'Data encryption',
      'Access logging and monitoring'
    ],
    system_generated: true
  },

  {
    activity_name: 'Website Analytics',
    controller_name: 'GenAForm SaaS Platform',
    controller_contact: '<EMAIL>',
    purposes: ['Website optimization', 'User experience improvement', 'Performance monitoring'],
    legal_bases: [LegalBasis.CONSENT, LegalBasis.LEGITIMATE_INTERESTS],
    data_subject_categories: [DataSubjectCategory.WEBSITE_VISITORS, DataSubjectCategory.CUSTOMERS],
    personal_data_categories: [
      PersonalDataCategory.TECHNICAL_DATA,
      PersonalDataCategory.USAGE_DATA,
      PersonalDataCategory.BEHAVIORAL_DATA
    ],
    recipients: ['Analytics service providers', 'Internal development team'],
    retention_periods: [
      {
        data_category: PersonalDataCategory.TECHNICAL_DATA,
        retention_period: '2 years for analytics purposes',
        retention_days: 730,
        legal_basis: 'Legitimate interests in service improvement',
        deletion_method: 'anonymize'
      }
    ],
    security_measures: [
      'IP address anonymization',
      'Cookie consent management',
      'Data minimization practices',
      'Regular data purging'
    ],
    system_generated: true
  },

  {
    activity_name: 'Customer Support',
    controller_name: 'GenAForm SaaS Platform',
    controller_contact: '<EMAIL>',
    purposes: ['Customer service provision', 'Issue resolution', 'Service improvement'],
    legal_bases: [LegalBasis.CONTRACT, LegalBasis.LEGITIMATE_INTERESTS],
    data_subject_categories: [DataSubjectCategory.CUSTOMERS],
    personal_data_categories: [
      PersonalDataCategory.IDENTIFICATION_DATA,
      PersonalDataCategory.CONTACT_DATA,
      PersonalDataCategory.USAGE_DATA,
      PersonalDataCategory.TECHNICAL_DATA
    ],
    recipients: ['Customer support team', 'Technical support providers'],
    retention_periods: [
      {
        data_category: PersonalDataCategory.USAGE_DATA,
        retention_period: '1 year after case closure',
        retention_days: 365,
        legal_basis: 'Service improvement and quality assurance',
        deletion_method: 'anonymize'
      }
    ],
    security_measures: [
      'Access controls for support staff',
      'Ticket encryption and security',
      'Data access logging',
      'Regular training on data protection'
    ],
    system_generated: true
  }
];

/**
 * Log a processing activity to the register
 */
export async function logProcessingActivity(
  activity: Omit<ProcessingActivity, 'id' | 'created_at' | 'updated_at'>,
  userId?: string
): Promise<string | null> {
  try {
    const { data, error } = await (supabaseAdmin as any)
      .from('processing_activities')
      .insert({
        ...activity,
        user_id: userId || null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select('id')
      .single();

    if (error) {
      console.error('Failed to log processing activity:', error);
      return null;
    }

    return data.id;
  } catch (error) {
    console.error('Error logging processing activity:', error);
    return null;
  }
}

/**
 * Get all processing activities from the register
 */
export async function getProcessingActivities(): Promise<ProcessingActivity[]> {
  try {
    const { data, error } = await (supabaseAdmin as any)
      .from('processing_activities')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Failed to fetch processing activities:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error fetching processing activities:', error);
    return [];
  }
}

/**
 * Get processing activities summary using secure function
 */
export async function getProcessingActivitiesSummary(): Promise<any[]> {
  try {
    const { data, error } = await (supabaseAdmin as any)
      .rpc('get_processing_activities_report');

    if (error) {
      console.error('Failed to fetch processing activities summary:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error fetching processing activities summary:', error);
    return [];
  }
}

/**
 * Initialize standard processing activities
 */
export async function initializeStandardActivities(): Promise<void> {
  try {
    // Check if activities already exist
    const { data: existing } = await (supabaseAdmin as any)
      .from('processing_activities')
      .select('activity_name')
      .eq('system_generated', true);

    const existingNames = existing?.map((a: any) => a.activity_name) || [];

    // Insert only new activities
    const newActivities = STANDARD_PROCESSING_ACTIVITIES.filter(
      activity => !existingNames.includes(activity.activity_name)
    );

    if (newActivities.length > 0) {
      const { error } = await (supabaseAdmin as any)
        .from('processing_activities')
        .insert(newActivities.map(activity => ({
          ...activity,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })));

      if (error) {
        console.error('Failed to initialize standard activities:', error);
      } else {
        console.log(`Initialized ${newActivities.length} standard processing activities`);
      }
    }
  } catch (error) {
    console.error('Error initializing standard activities:', error);
  }
}
