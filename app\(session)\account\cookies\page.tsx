import { ModernDashboardLayout } from "@/components/dashboard/modern-layout";
import { AccountCookieSettings } from "@/components/account/cookie-settings";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, Info, Shield, BarChart3, Target } from "lucide-react";

export default function CookieSettingsPage() {
  return (
    <ModernDashboardLayout 
      title="Cookie Settings" 
      subtitle="Manage your cookie preferences and understand how we use cookies"
    >
      <div className="space-y-6">
        {/* Cookie Settings */}
        <AccountCookieSettings />

        {/* Cookie Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Info className="w-5 h-5 text-blue-500" />
              About Our Cookies
            </CardTitle>
            <CardDescription>
              Learn more about the different types of cookies we use and their purposes
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Necessary Cookies Info */}
            <div className="p-4 border rounded-lg bg-blue-50 dark:bg-blue-950">
              <div className="flex items-start gap-3">
                <Shield className="w-5 h-5 text-blue-500 mt-0.5" />
                <div>
                  <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                    Necessary Cookies (Always Active)
                  </h4>
                  <p className="text-sm text-blue-800 dark:text-blue-200 mb-3">
                    These cookies are essential for the website to function properly and cannot be disabled.
                  </p>
                  <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                    <li>• Authentication and session management</li>
                    <li>• Security and fraud prevention</li>
                    <li>• Basic website functionality</li>
                    <li>• User preferences and settings</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Analytics Cookies Info */}
            <div className="p-4 border rounded-lg">
              <div className="flex items-start gap-3">
                <BarChart3 className="w-5 h-5 text-green-500 mt-0.5" />
                <div>
                  <h4 className="font-medium mb-2">Analytics Cookies (Optional)</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    Help us understand how visitors interact with our website to improve user experience.
                  </p>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Page views and user journeys</li>
                    <li>• Feature usage and performance metrics</li>
                    <li>• Error tracking and debugging</li>
                    <li>• Website optimization insights</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Marketing Cookies Info */}
            <div className="p-4 border rounded-lg">
              <div className="flex items-start gap-3">
                <Target className="w-5 h-5 text-purple-500 mt-0.5" />
                <div>
                  <h4 className="font-medium mb-2">Marketing Cookies (Optional)</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    Used to deliver personalized advertisements and measure campaign effectiveness.
                  </p>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Personalized advertising content</li>
                    <li>• Campaign performance tracking</li>
                    <li>• Cross-platform user identification</li>
                    <li>• Retargeting and remarketing</li>
                  </ul>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Browser Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Cookie className="w-5 h-5 text-orange-500" />
              Browser Cookie Settings
            </CardTitle>
            <CardDescription>
              You can also manage cookies directly through your browser settings
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Most browsers allow you to control cookies through their settings. Here&apos;s how to access
                cookie settings in popular browsers:
              </p>
              
              <div className="grid gap-3 md:grid-cols-2">
                <div className="p-3 border rounded-lg">
                  <h4 className="font-medium mb-1">Chrome</h4>
                  <p className="text-sm text-muted-foreground">
                    Settings → Privacy and security → Cookies and other site data
                  </p>
                </div>
                
                <div className="p-3 border rounded-lg">
                  <h4 className="font-medium mb-1">Firefox</h4>
                  <p className="text-sm text-muted-foreground">
                    Settings → Privacy & Security → Cookies and Site Data
                  </p>
                </div>
                
                <div className="p-3 border rounded-lg">
                  <h4 className="font-medium mb-1">Safari</h4>
                  <p className="text-sm text-muted-foreground">
                    Preferences → Privacy → Manage Website Data
                  </p>
                </div>
                
                <div className="p-3 border rounded-lg">
                  <h4 className="font-medium mb-1">Edge</h4>
                  <p className="text-sm text-muted-foreground">
                    Settings → Cookies and site permissions → Cookies and site data
                  </p>
                </div>
              </div>

              <div className="bg-yellow-50 dark:bg-yellow-950 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3">
                <p className="text-sm text-yellow-800 dark:text-yellow-200">
                  <strong>Note:</strong> Disabling necessary cookies may affect the functionality of our service 
                  and prevent you from accessing certain features.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Privacy Links */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium mb-1">Need More Information?</h4>
                <p className="text-sm text-muted-foreground">
                  Learn more about our privacy practices and your rights
                </p>
              </div>
              <div className="flex gap-2">
                <a 
                  href="/privacy" 
                  className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                >
                  Privacy Policy
                </a>
                <span className="text-muted-foreground">•</span>
                <a 
                  href="/cookies" 
                  className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                >
                  Cookie Policy
                </a>
                <span className="text-muted-foreground">•</span>
                <a 
                  href="/gdpr" 
                  className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                >
                  Your Rights
                </a>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </ModernDashboardLayout>
  );
}
